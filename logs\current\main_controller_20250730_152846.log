2025-07-30 15:28:46,681 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:28:46,683 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:28:46,688 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:28:46,698 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:28:46,701 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:28:46,704 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:28:46,708 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:28:46,712 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 15:28:46,715 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:28:46,730 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:28:46,730 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:28:46,737 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:28:46,749 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:28:46,794 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:28:46,808 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_152846.log
2025-07-30 15:28:46,810 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:28:46,812 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:28:46,813 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:28:46,815 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 15:28:46,816 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 15:28:46,816 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 15:28:46,818 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 23:28:46
2025-07-30 15:28:46,818 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 15:28:46,819 - __main__ - INFO - 📅 启动时间: 2025-07-30 23:28:46
2025-07-30 15:28:46,819 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:28:46,820 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:28:47,362 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:28:47,362 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:28:47,892 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:28:47,893 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:28:47,894 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:28:47,895 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:28:47,895 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 15:28:47,895 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 15:28:47,895 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:28:47,896 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:28:47,896 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:28:47,896 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:28:47,897 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 6032430)
2025-07-30 15:28:47,898 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 6621532)
2025-07-30 15:28:47,899 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 15:28:47,899 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 15:28:47,900 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 6032430)
2025-07-30 15:28:47,901 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 15:28:48,215 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 6621532)
2025-07-30 15:28:48,220 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-30 15:28:48,243 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 15:28:48,252 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 15:28:48,263 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 15:28:48,281 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 15:28:48,296 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 15:28:48,313 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 15:28:49,916 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 15:28:49,919 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 15:28:49,922 - __main__ - INFO - 📋 待处理联系人数: 2840
2025-07-30 15:28:49,930 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 15:28:49,935 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2840
2025-07-30 15:28:49,947 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 2
2025-07-30 15:28:49,951 - __main__ - INFO - 
============================================================
2025-07-30 15:28:49,963 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 15:28:49,964 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:28:49,965 - __main__ - INFO - 📊 全局进度：已处理 0/2840 个联系人（剩余 2840 个）
2025-07-30 15:28:49,966 - __main__ - INFO - ============================================================
2025-07-30 15:28:49,966 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:28:49,968 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:28:49,970 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:28:49,977 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:28:49,984 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 6032430)
2025-07-30 15:28:49,985 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-07-30 15:28:49,998 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 15:28:51,001 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:28:52,102 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:28:52,103 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6032430
2025-07-30 15:28:52,103 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:28:52,410 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:28:52,410 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:28:52,411 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:28:52,411 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:28:52,411 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:28:52,412 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:28:52,412 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:28:52,413 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:28:52,413 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:28:52,413 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:28:52,615 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:28:52,615 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:28:52,616 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6032430 (API返回: None)
2025-07-30 15:28:52,917 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:28:52,917 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:28:52,918 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:28:53,918 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:28:53,919 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:28:53,923 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:28:53,924 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:28:53,925 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:28:53,926 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:28:53,926 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:28:53,927 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:28:53,927 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:28:54,128 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:28:54,129 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:28:54,130 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:28:56,524 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:28:56,525 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:28:56,527 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 15:28:59,274 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:28:59,475 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:28:59,476 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:28:59,476 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:29:01,857 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:29:01,858 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:29:01,859 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 15:29:04,684 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:29:04,885 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:29:04,885 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:29:04,886 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:29:07,275 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:29:07,276 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:29:07,276 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 15:29:09,412 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:29:09,613 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:29:09,614 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:29:09,614 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:29:12,055 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:29:12,056 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:29:12,057 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 15:29:13,665 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:29:13,865 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:29:13,866 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:29:13,866 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:29:16,239 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:29:16,239 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:29:16,239 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:29:16,240 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:29:16,240 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:29:16,243 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:29:16,243 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:29:16,246 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6096782, 进程: Weixin.exe)
2025-07-30 15:29:16,248 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:29:16,249 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:29:16,256 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:29:16,262 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 6096782)
2025-07-30 15:29:16,262 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6096782) - 增强版
2025-07-30 15:29:16,566 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:29:16,567 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:29:16,567 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:29:16,567 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:29:16,568 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:29:16,569 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:29:16,774 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:29:16,774 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:29:16,976 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:29:16,977 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:29:16,977 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:29:16,977 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:29:16,977 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:29:16,978 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:29:16,978 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:29:17,978 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:29:17,979 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:29:17,981 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:29:17,981 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:29:17,982 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6096782, 进程: Weixin.exe)
2025-07-30 15:29:17,984 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:29:17,984 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:29:17,988 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:29:17,989 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:29:17,990 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:29:17,992 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:29:17,994 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:29:17,995 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6032430
2025-07-30 15:29:17,996 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:29:18,307 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:29:18,307 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:29:18,308 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:29:18,308 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:29:18,308 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:29:18,308 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:29:18,309 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:29:18,309 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:29:18,309 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:29:18,310 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:29:18,512 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:29:18,512 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:29:18,514 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6032430 (API返回: None)
2025-07-30 15:29:18,815 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:29:18,815 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:29:18,815 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:29:18,816 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:29:19,817 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:29:19,817 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:29:19,817 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:29:19,828 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_152919.log
2025-07-30 15:29:19,832 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:29:19,832 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:29:19,833 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:29:19,833 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 15:29:19,833 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:29:19,834 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:29:19,838 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 15:29:19,846 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:29:19,847 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 15:29:19,848 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:29:19,849 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 15:29:19,850 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 15:29:19,850 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:29:19,851 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:29:19,853 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6096782
2025-07-30 15:29:19,853 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6096782) - 增强版
2025-07-30 15:29:20,167 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:29:20,167 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:29:20,168 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:29:20,168 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:29:20,170 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:29:20,171 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:29:20,171 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:29:20,172 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:29:20,385 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:29:20,386 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:29:20,389 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6096782 (API返回: None)
2025-07-30 15:29:20,690 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:29:20,690 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:29:20,691 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:29:20,691 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:29:20,692 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:29:20,693 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:29:20,693 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:29:20,698 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:29:20,698 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:29:21,377 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:29:21,378 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:29:21,669 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2840 个
2025-07-30 15:29:21,670 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2840 个 (总计: 3135 个)
2025-07-30 15:29:21,671 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:29:21,671 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:29:21,672 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:29:21,672 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:29:21,673 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:29:21,674 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:29:21,674 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2840
2025-07-30 15:29:21,674 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18681482191 (朱文茜)
2025-07-30 15:29:21,675 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:29:21,676 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:29:21,679 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:29:28,295 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18681482191
2025-07-30 15:29:28,295 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:29:28,296 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18681482191 执行添加朋友操作...
2025-07-30 15:29:28,296 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:29:28,296 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:29:28,297 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:29:28,298 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:29:28,303 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:29:28,304 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:29:28,306 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:29:28,307 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:29:28,307 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:29:28,308 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:29:28,309 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:29:28,310 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:29:28,313 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:29:28,317 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:29:28,324 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:29:28,328 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:29:28,334 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 15:29:28,336 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:29:28,838 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:29:28,840 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:29:28,934 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.49, 边缘比例0.0378
2025-07-30 15:29:28,941 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_152928.png
2025-07-30 15:29:28,943 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:29:28,944 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:29:28,945 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:29:28,946 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:29:28,948 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:29:28,955 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_152928.png
2025-07-30 15:29:28,960 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:29:28,965 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:29:28,970 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:29:28,973 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:29:28,975 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:29:28,978 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:29:28,980 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:29:28,981 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:29:28,989 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_152928.png
2025-07-30 15:29:28,992 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:29:28,993 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:29:28,998 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_152928.png
2025-07-30 15:29:29,070 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:29:29,071 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:29:29,072 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:29:29,073 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:29:29,374 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:29:30,155 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:29:30,157 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:29:30,163 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:29:30,163 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:29:30,165 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:29:30,166 - modules.wechat_auto_add_simple - INFO - ✅ 18681482191 添加朋友操作执行成功
2025-07-30 15:29:30,166 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:29:30,166 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:29:30,169 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:29:30,169 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:29:32,172 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:29:32,172 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:29:32,173 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:29:32,173 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:29:32,174 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:29:32,174 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:29:32,175 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:29:32,175 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:29:32,176 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:29:32,176 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18681482191
2025-07-30 15:29:32,181 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:29:32,182 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:29:32,182 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:29:32,183 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:29:32,184 - modules.friend_request_window - INFO -    📱 phone: '18681482191'
2025-07-30 15:29:32,185 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:29:32,185 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:29:32,779 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:29:32,780 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:29:32,780 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:29:32,781 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:29:32,782 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18681482191
2025-07-30 15:29:32,782 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:29:32,783 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:29:32,784 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:29:32,785 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:29:32,786 - modules.friend_request_window - INFO -    📱 手机号码: 18681482191
2025-07-30 15:29:32,787 - modules.friend_request_window - INFO -    🆔 准考证: 014325120008
2025-07-30 15:29:32,787 - modules.friend_request_window - INFO -    👤 姓名: 朱文茜
2025-07-30 15:29:32,788 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:29:32,788 - modules.friend_request_window - INFO -    📝 备注格式: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:32,789 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:29:32,789 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:32,789 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:29:32,790 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1772556, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:29:32,793 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1772556)
2025-07-30 15:29:32,793 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:29:32,793 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:29:32,794 - modules.friend_request_window - INFO - 🔄 激活窗口: 1772556
2025-07-30 15:29:33,497 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:29:33,497 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:29:33,498 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:29:33,499 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:29:33,500 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:29:33,501 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:29:33,502 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:29:33,504 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:29:33,504 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:29:33,505 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:29:33,505 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:29:33,505 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:29:33,505 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:29:33,506 - modules.friend_request_window - INFO -    📝 remark参数: '014325120008-朱文茜-2025-07-30 23:29:32' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:29:33,506 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:29:33,507 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:33,508 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:29:33,509 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:29:33,510 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:29:33,510 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:29:33,511 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:29:33,511 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:29:33,511 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:29:34,421 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:29:39,667 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:29:39,668 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:29:39,669 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:29:39,669 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:29:39,671 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 15:24:27,175 - modules.friend_request_w...' (前50字符)
2025-07-30 15:29:39,984 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:29:39,984 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:29:40,887 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:29:40,897 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:29:40,898 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:29:40,899 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:29:40,899 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:29:40,900 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:29:41,400 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:29:41,401 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:29:41,401 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:29:41,402 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:29:41,402 - modules.friend_request_window - INFO -    📝 内容: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:41,403 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:29:41,403 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120008-\xe6\x9c\xb1\xe6\x96\x87\xe8\x8c\x9c-2025-07-30 23:29:32'
2025-07-30 15:29:41,403 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:29:42,322 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:29:47,582 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:29:47,583 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:29:47,583 - modules.friend_request_window - INFO -    📝 原始文本: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:47,583 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:29:47,584 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 15:24:27,175 - modules.friend_request_w...' (前50字符)
2025-07-30 15:29:47,894 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:29:47,895 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:29:48,798 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:29:48,810 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:29:48,810 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:48,811 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:29:48,811 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:48,812 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:29:49,312 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:29:49,313 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:29:49,313 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:29:49,313 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:29:49,314 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:29:49,314 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:29:49,314 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:29:50,115 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:29:50,115 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:29:50,116 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:29:50,721 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:29:50,722 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:29:50,724 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:29:50,724 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:29:50,725 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:29:50,725 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:29:51,227 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:29:51,229 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:29:51,229 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:29:51,229 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:29:51,230 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:29:51,230 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:29:51,230 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:29:51,231 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:29:51,231 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:29:51,232 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:29:51,232 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:29:51,232 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:29:51,234 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:29:51,235 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:29:51,236 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:29:51,237 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:29:51,239 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:29:51,245 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 3
2025-07-30 15:29:51,246 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 15:29:51,247 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 724402)
2025-07-30 15:29:51,252 - modules.friend_request_window - INFO - ✅ 频率错误窗口已强制添加到黑名单
2025-07-30 15:29:51,252 - modules.friend_request_window - INFO - 📊 当前黑名单窗口数量: 1
2025-07-30 15:29:51,254 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 15:29:51,255 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 15:29:51,759 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 15:29:51,759 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 15:29:51,759 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 15:29:51,760 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 15:29:51,760 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 15:29:51,760 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 15:29:51,761 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 15:29:51,761 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 15:29:52,671 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 15:29:52,672 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 15:29:52,672 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 15:29:52,673 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 15:29:52,696 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 15:29:52,697 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 15:29:53,502 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 15:29:53,502 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 15:29:53,502 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 15:29:53,503 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 15:29:53,503 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 15:29:53,503 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 15:29:53,504 - modules.friend_request_window - INFO - 🎯 强制使用坐标(700, 16)关闭微信主窗口
2025-07-30 15:29:53,504 - modules.friend_request_window - INFO -    激活窗口...
2025-07-30 15:29:54,004 - modules.friend_request_window - INFO -    使用坐标(700, 16)点击关闭按钮...
2025-07-30 15:29:54,007 - modules.friend_request_window - INFO - 📊 关闭前微信窗口数量: 2
2025-07-30 15:29:55,147 - modules.friend_request_window - INFO - 🔥 强制终止微信进程确保真正关闭...
2025-07-30 15:29:55,147 - modules.friend_request_window - INFO - 🔥 开始强制终止微信进程...
2025-07-30 15:29:55,148 - modules.friend_request_window - INFO - 📋 目标进程ID: 33068
2025-07-30 15:29:55,148 - modules.friend_request_window - INFO - 📋 进程名称: Weixin.exe
2025-07-30 15:29:55,148 - modules.friend_request_window - INFO - 🔄 方法1: 优雅终止进程...
2025-07-30 15:29:55,551 - modules.friend_request_window - INFO - ✅ 进程已优雅终止
2025-07-30 15:29:55,552 - modules.friend_request_window - INFO - ✅ 进程终止成功
2025-07-30 15:29:56,554 - modules.friend_request_window - INFO - 📊 关闭后微信窗口数量: 1
2025-07-30 15:29:56,555 - modules.friend_request_window - INFO - ✅ 第一次坐标点击+进程终止成功关闭微信主窗口
2025-07-30 15:29:56,555 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 15:29:56,555 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 15:29:56,555 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 15:29:58,556 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 15:29:58,557 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:29:58,558 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 15:29:58,558 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:29:58,560 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:29:58,560 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:29:58,563 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:29:58,564 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 15:29:58,565 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 15:29:58,592 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 15:29:58,592 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 15:29:58,592 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 15:29:58,593 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 1179804
2025-07-30 15:29:58,594 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:29:58,595 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1179804) - 增强版
2025-07-30 15:29:58,595 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 15:29:59,108 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:29:59,108 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:29:59,108 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 640x480, 面积=307200
2025-07-30 15:29:59,109 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:29:59,110 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:29:59,110 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:29:59,111 - modules.window_manager - INFO - 📏 当前窗口位置: (640, 276), 大小: 640x480
2025-07-30 15:29:59,111 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:29:59,111 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 15:29:59,418 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 240x82
2025-07-30 15:29:59,418 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:29:59,418 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(483, 568)
2025-07-30 15:29:59,419 - modules.window_manager - WARNING - ⚠️ 窗口验证失败 - 位置差异: (0, 0), 大小差异: (483, 568)
2025-07-30 15:29:59,419 - modules.window_manager - WARNING - ⚠️ 窗口大小和位置调整未达到预期效果
2025-07-30 15:29:59,419 - modules.window_manager - WARNING - ⚠️ 微信主窗口大小调整失败，尝试仅移动位置
2025-07-30 15:29:59,419 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 240x82
2025-07-30 15:29:59,420 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:29:59,420 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:29:59,420 - modules.window_manager - INFO - 📍 微信主窗口位置移动成功
2025-07-30 15:29:59,622 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:29:59,623 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:29:59,623 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 15:29:59,623 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 15:29:59,624 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 15:29:59,624 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 15:29:59,624 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 15:30:01,625 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 15:30:01,626 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:30:01,627 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:30:01,627 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 15:30:01,627 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 15:30:01,628 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 15:30:01,628 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:30:01,628 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:30:01,629 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:30:01,629 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:30:01,629 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:30:01,629 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:30:01,830 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:30:01,830 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:30:01,831 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:30:04,220 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:30:04,220 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:30:04,221 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 15:30:06,182 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:30:06,383 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:30:06,384 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:30:06,384 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:30:08,770 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:30:08,770 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:30:08,771 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.5 秒...
2025-07-30 15:30:10,299 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:30:10,500 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:30:10,501 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:30:10,501 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:30:12,886 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:30:12,886 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:30:12,887 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 15:30:15,654 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:30:15,855 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:30:15,855 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:30:15,855 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:30:18,238 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:30:18,238 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:30:18,239 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 15:30:20,239 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:30:20,440 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:30:20,441 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:30:20,441 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:30:22,820 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:30:22,820 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:30:22,821 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:30:22,822 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:30:22,822 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:30:22,822 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:30:22,825 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 855560, 进程: Weixin.exe)
2025-07-30 15:30:22,825 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:30:22,827 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:30:22,831 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:30:22,834 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 855560)
2025-07-30 15:30:22,834 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 855560) - 增强版
2025-07-30 15:30:23,137 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:30:23,138 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:30:23,138 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:30:23,139 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:30:23,139 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:30:23,140 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:30:23,345 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:30:23,345 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:30:23,548 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:30:23,548 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:30:23,548 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:30:23,549 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:30:23,549 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:30:23,549 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:30:23,550 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:30:24,550 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:30:24,551 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:30:24,552 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:30:24,553 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 855560, 进程: Weixin.exe)
2025-07-30 15:30:24,554 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:30:24,554 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:30:24,557 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:30:24,558 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:30:24,559 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:30:24,559 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:30:24,560 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 15:30:24,560 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 15:30:24,561 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 15:30:25,565 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 15:30:25,566 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 15:30:25,567 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 15:30:25,567 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 15:30:25,568 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 15:30:25,568 - modules.friend_request_window - INFO - 🔧 开始移动窗口 855560 到目标位置...
2025-07-30 15:30:26,375 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 15:30:26,376 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 15:30:26,376 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 15:30:26,376 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 15:30:26,376 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 15:30:26,377 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 15:30:26,377 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 15:30:26,378 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 15:30:26,378 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 15:30:26,378 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 15:30:26,378 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 15:30:26,379 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:30:26,379 - modules.friend_request_window - INFO -    📝 备注信息: '014325120008-朱文茜-2025-07-30 23:29:32'
2025-07-30 15:30:26,879 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 15:30:26,881 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:30:26,882 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:30:26,884 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:30:26,884 - modules.wechat_auto_add_simple - INFO - ✅ 18681482191 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 15:30:26,884 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18681482191
2025-07-30 15:30:26,885 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:30:26,886 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:30:26,887 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:30:30,699 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2840
2025-07-30 15:30:30,699 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15821673407 (何瀚禹)
2025-07-30 15:30:30,700 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:30:30,700 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:30:30,702 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:30:37,291 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15821673407
2025-07-30 15:30:37,291 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:30:37,291 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15821673407 执行添加朋友操作...
2025-07-30 15:30:37,291 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:30:37,292 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:30:37,293 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:30:37,294 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:30:37,298 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:30:37,299 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:30:37,300 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:30:37,301 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:30:37,301 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:30:37,301 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:30:37,302 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:30:37,305 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:30:37,310 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:30:37,312 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:30:37,313 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:30:37,315 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 15:30:37,317 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:30:37,820 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:30:37,821 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:30:37,884 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差34.88, 边缘比例0.0369
2025-07-30 15:30:37,892 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_153037.png
2025-07-30 15:30:37,900 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:30:37,903 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:30:37,907 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:30:37,910 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:30:37,911 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:30:37,921 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_153037.png
2025-07-30 15:30:37,924 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:30:37,926 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:30:37,927 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:30:37,928 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:30:37,931 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:30:37,937 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:30:37,938 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:30:37,940 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:30:37,949 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_153037.png
2025-07-30 15:30:37,954 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:30:37,955 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:30:37,961 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_153037.png
2025-07-30 15:30:37,993 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:30:37,996 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:30:38,001 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:30:38,004 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:30:38,305 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:30:39,085 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:30:39,086 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:30:39,088 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:30:39,088 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:30:39,091 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:30:39,091 - modules.wechat_auto_add_simple - INFO - ✅ 15821673407 添加朋友操作执行成功
2025-07-30 15:30:39,092 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:30:39,093 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:30:39,096 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:30:39,097 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:30:41,101 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 申请添加朋友
2025-07-30 15:30:41,101 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:30:41,102 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:30:41,102 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:30:41,102 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:30:41,103 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:30:41,103 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:30:41,103 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:30:41,103 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:30:41,104 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15821673407
2025-07-30 15:30:41,104 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:30:41,105 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:30:41,105 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:30:41,105 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:30:41,105 - modules.friend_request_window - INFO -    📱 phone: '15821673407'
2025-07-30 15:30:41,106 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:30:41,106 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:30:41,741 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:30:41,741 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:30:41,742 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:30:41,742 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:30:41,743 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15821673407
2025-07-30 15:30:41,743 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:30:41,744 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:30:41,744 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:30:41,745 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:30:41,745 - modules.friend_request_window - INFO -    📱 手机号码: 15821673407
2025-07-30 15:30:41,745 - modules.friend_request_window - INFO -    🆔 准考证: 014325120010
2025-07-30 15:30:41,745 - modules.friend_request_window - INFO -    👤 姓名: 何瀚禹
2025-07-30 15:30:41,746 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:30:41,746 - modules.friend_request_window - INFO -    📝 备注格式: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:41,749 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:30:41,750 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:41,751 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:30:41,752 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 920020, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:30:41,754 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 920020)
2025-07-30 15:30:41,754 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:30:41,756 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:30:41,756 - modules.friend_request_window - INFO - 🔄 激活窗口: 920020
2025-07-30 15:30:42,459 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:30:42,459 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:30:42,460 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:30:42,460 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:30:42,460 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:30:42,461 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:30:42,461 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:30:42,461 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:30:42,461 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:30:42,462 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:30:42,462 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:30:42,462 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:30:42,462 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:30:42,463 - modules.friend_request_window - INFO -    📝 remark参数: '014325120010-何瀚禹-2025-07-30 23:30:41' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:30:42,463 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:30:42,463 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:42,467 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:30:42,467 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:30:42,468 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:30:42,468 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:30:42,468 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:30:42,469 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:30:42,469 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:30:43,406 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:30:48,653 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:30:48,653 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:30:48,654 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:30:48,654 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:30:48,655 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 15:24:27,175 - modules.friend_request_w...' (前50字符)
2025-07-30 15:30:48,965 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:30:48,966 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:30:49,868 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:30:49,882 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:30:49,883 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:30:49,884 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:30:49,885 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:30:49,885 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:30:50,386 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:30:50,387 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:30:50,387 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:30:50,387 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:30:50,388 - modules.friend_request_window - INFO -    📝 内容: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:50,388 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:30:50,388 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120010-\xe4\xbd\x95\xe7\x80\x9a\xe7\xa6\xb9-2025-07-30 23:30:41'
2025-07-30 15:30:50,388 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:30:51,300 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:30:56,555 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:30:56,555 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:30:56,556 - modules.friend_request_window - INFO -    📝 原始文本: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:56,556 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:30:56,556 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 15:24:27,175 - modules.friend_request_w...' (前50字符)
2025-07-30 15:30:56,867 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:30:56,867 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:30:57,770 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:30:57,782 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:30:57,783 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:57,783 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:30:57,784 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:57,784 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:30:58,285 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:30:58,286 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:30:58,286 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:30:58,286 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:30:58,287 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:30:58,287 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:30:58,287 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:30:59,088 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:30:59,089 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:30:59,089 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:30:59,699 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:30:59,702 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:30:59,708 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:30:59,708 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:30:59,709 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:30:59,710 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:31:00,217 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:31:00,221 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:31:00,222 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:31:00,223 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:31:00,223 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:31:00,226 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:31:00,230 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:31:00,235 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:31:00,235 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:31:00,236 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:31:00,237 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:31:00,238 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:31:00,238 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:31:00,240 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:31:00,241 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:31:00,242 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:31:00,242 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:31:00,245 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 3
2025-07-30 15:31:00,245 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 15:31:00,248 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 3084120)
2025-07-30 15:31:00,253 - modules.friend_request_window - INFO - ✅ 频率错误窗口已强制添加到黑名单
2025-07-30 15:31:00,254 - modules.friend_request_window - INFO - 📊 当前黑名单窗口数量: 1
2025-07-30 15:31:00,254 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 15:31:00,255 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 15:31:00,757 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 15:31:00,757 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 15:31:00,757 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 15:31:00,758 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 15:31:00,758 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 15:31:00,758 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 15:31:00,758 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 15:31:00,759 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 15:31:01,666 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 15:31:01,667 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 15:31:01,667 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 15:31:01,667 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 15:31:01,687 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 15:31:01,688 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 15:31:02,489 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 15:31:02,489 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 15:31:02,490 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 15:31:02,490 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 15:31:02,490 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 15:31:02,491 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 15:31:02,491 - modules.friend_request_window - INFO - 🎯 强制使用坐标(700, 16)关闭微信主窗口
2025-07-30 15:31:02,491 - modules.friend_request_window - INFO -    激活窗口...
2025-07-30 15:31:02,992 - modules.friend_request_window - INFO -    使用坐标(700, 16)点击关闭按钮...
2025-07-30 15:31:02,995 - modules.friend_request_window - INFO - 📊 关闭前微信窗口数量: 2
2025-07-30 15:31:04,119 - modules.friend_request_window - INFO - 🔥 强制终止微信进程确保真正关闭...
2025-07-30 15:31:04,120 - modules.friend_request_window - INFO - 🔥 开始强制终止微信进程...
2025-07-30 15:31:04,120 - modules.friend_request_window - INFO - 📋 目标进程ID: 34892
2025-07-30 15:31:04,120 - modules.friend_request_window - INFO - 📋 进程名称: Weixin.exe
2025-07-30 15:31:04,121 - modules.friend_request_window - INFO - 🔄 方法1: 优雅终止进程...
2025-07-30 15:31:04,537 - modules.friend_request_window - INFO - ✅ 进程已优雅终止
2025-07-30 15:31:04,538 - modules.friend_request_window - INFO - ✅ 进程终止成功
2025-07-30 15:31:05,541 - modules.friend_request_window - INFO - 📊 关闭后微信窗口数量: 0
2025-07-30 15:31:05,542 - modules.friend_request_window - INFO - ✅ 第一次坐标点击+进程终止成功关闭微信主窗口
2025-07-30 15:31:05,542 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 15:31:05,542 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 15:31:05,543 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 15:31:07,543 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 15:31:07,545 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:31:07,546 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 15:31:07,547 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:31:07,551 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-30 15:31:07,552 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 15:31:07,552 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 15:31:07,573 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 15:31:07,574 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 15:31:07,574 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 15:31:07,575 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 527782
2025-07-30 15:31:07,576 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:31:07,576 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 527782) - 增强版
2025-07-30 15:31:07,577 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 15:31:08,101 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:31:08,101 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:31:08,102 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 640x480, 面积=307200
2025-07-30 15:31:08,102 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:31:08,103 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:31:08,103 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:31:08,104 - modules.window_manager - INFO - 📏 当前窗口位置: (640, 276), 大小: 640x480
2025-07-30 15:31:08,104 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:31:08,104 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 15:31:08,411 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 240x82
2025-07-30 15:31:08,411 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:31:08,411 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(483, 568)
2025-07-30 15:31:08,412 - modules.window_manager - WARNING - ⚠️ 窗口验证失败 - 位置差异: (0, 0), 大小差异: (483, 568)
2025-07-30 15:31:08,412 - modules.window_manager - WARNING - ⚠️ 窗口大小和位置调整未达到预期效果
2025-07-30 15:31:08,412 - modules.window_manager - WARNING - ⚠️ 微信主窗口大小调整失败，尝试仅移动位置
2025-07-30 15:31:08,413 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 240x82
2025-07-30 15:31:08,413 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:31:08,413 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:31:08,413 - modules.window_manager - INFO - 📍 微信主窗口位置移动成功
2025-07-30 15:31:08,614 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:31:08,615 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:31:08,615 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 15:31:08,615 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 15:31:08,616 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 15:31:08,616 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 15:31:08,616 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 15:31:10,650 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 15:31:10,698 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:31:10,759 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:31:10,815 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 15:31:10,860 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 15:31:10,886 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 15:31:10,926 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:31:10,969 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:31:11,089 - modules.main_interface - WARNING - ⚠️ 当前前台窗口不是微信窗口: 'main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code' (类名: Chrome_WidgetWin_1)
2025-07-30 15:31:11,227 - modules.main_interface - INFO - 🔄 尝试查找并激活微信窗口...
2025-07-30 15:31:11,305 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:31:11,384 - modules.main_interface - INFO - 🔄 创建新的window_manager查找并激活微信窗口...
2025-07-30 15:31:11,476 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:31:11,757 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-30 15:31:11,823 - modules.main_interface - ERROR - ❌ 未找到微信窗口
2025-07-30 15:31:11,886 - modules.main_interface - ERROR - ❌ 无法找到或激活微信窗口
2025-07-30 15:31:11,983 - modules.main_interface - ERROR - ❌ [主界面操作] 微信窗口验证失败
2025-07-30 15:31:12,056 - modules.friend_request_window - ERROR - ❌ 切换窗口后主界面操作流程执行失败
2025-07-30 15:31:12,144 - modules.friend_request_window - ERROR - ❌ 切换窗口后主界面操作失败
2025-07-30 15:31:12,174 - modules.friend_request_window - ERROR - ❌ 所有频率错误处理方式都失败
2025-07-30 15:31:13,227 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:31:13,291 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 15:31:13,339 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 15:31:13,399 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 15:31:13,428 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 15:31:13,479 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:31:13,566 - modules.friend_request_window - INFO -    📝 备注信息: '014325120010-何瀚禹-2025-07-30 23:30:41'
2025-07-30 15:31:14,176 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 15:31:14,340 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:31:14,439 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:31:14,541 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:31:14,623 - modules.wechat_auto_add_simple - INFO - ✅ 15821673407 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 15:31:14,696 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15821673407
2025-07-30 15:31:14,777 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:31:14,835 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:31:14,907 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:31:19,303 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 15:31:19,333 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 15:31:19,366 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 15:31:19,389 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 15:31:19,439 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 15:31:19,483 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 15:31:19,519 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 15:31:19,561 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 15:31:19,600 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 15:31:19,632 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 15:31:19,688 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 15:31:19,736 - __main__ - INFO - � 更新全局进度：已处理 2/2838 个联系人（剩余 2838 个）
2025-07-30 15:31:19,874 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 15:31:22,922 - __main__ - INFO - 
============================================================
2025-07-30 15:31:22,923 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 15:31:22,923 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6621532)
2025-07-30 15:31:22,923 - __main__ - INFO - 📊 全局进度：已处理 2/2838 个联系人（剩余 2838 个）
2025-07-30 15:31:22,924 - __main__ - INFO - ============================================================
2025-07-30 15:31:22,924 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 15:31:22,925 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6621532)
2025-07-30 15:31:22,925 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:31:22,925 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 15:31:22,926 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 6621532)
2025-07-30 15:31:22,926 - __main__ - ERROR - ❌ 目标窗口已不存在: 微信
2025-07-30 15:31:22,927 - __main__ - ERROR - ❌ 步骤 1 执行失败，终止当前窗口处理
2025-07-30 15:31:22,930 - __main__ - ERROR - ❌ 第 2 个微信窗口处理失败
2025-07-30 15:31:22,931 - __main__ - INFO - ⏳ 失败后窗口切换延迟（3秒）...
2025-07-30 15:31:25,932 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 15:31:25,932 - __main__ - INFO - 📊 当前进度：已处理 2/2838 个联系人（剩余 2838 个）
2025-07-30 15:31:25,933 - __main__ - INFO - 
============================================================
2025-07-30 15:31:25,933 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 2 轮)
2025-07-30 15:31:25,934 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:31:25,934 - __main__ - INFO - 📊 全局进度：已处理 2/2838 个联系人（剩余 2838 个）
2025-07-30 15:31:25,934 - __main__ - INFO - ============================================================
2025-07-30 15:31:25,935 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:31:25,935 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:31:25,936 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:31:25,936 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:31:25,936 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 6032430)
2025-07-30 15:31:25,936 - __main__ - ERROR - ❌ 目标窗口已不存在: 微信
2025-07-30 15:31:25,937 - __main__ - ERROR - ❌ 步骤 1 执行失败，终止当前窗口处理
2025-07-30 15:31:25,937 - __main__ - ERROR - ❌ 第 1 个微信窗口处理失败
2025-07-30 15:31:25,937 - __main__ - INFO - ⏳ 失败后窗口切换延迟（3秒）...
2025-07-30 15:31:28,939 - __main__ - INFO - 
============================================================
2025-07-30 15:31:28,939 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 2 轮)
2025-07-30 15:31:28,939 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6621532)
2025-07-30 15:31:28,939 - __main__ - INFO - 📊 全局进度：已处理 2/2838 个联系人（剩余 2838 个）
2025-07-30 15:31:28,940 - __main__ - INFO - ============================================================
2025-07-30 15:31:28,940 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 15:31:28,940 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6621532)
2025-07-30 15:31:28,941 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:31:28,941 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 15:31:28,941 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 6621532)
2025-07-30 15:31:28,941 - __main__ - ERROR - ❌ 目标窗口已不存在: 微信
2025-07-30 15:31:28,942 - __main__ - ERROR - ❌ 步骤 1 执行失败，终止当前窗口处理
2025-07-30 15:31:28,942 - __main__ - ERROR - ❌ 第 2 个微信窗口处理失败
2025-07-30 15:31:28,942 - __main__ - INFO - ⏳ 失败后窗口切换延迟（3秒）...
