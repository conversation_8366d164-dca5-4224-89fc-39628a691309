2025-07-30 12:27:56,183 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:27:56,184 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:27:56,185 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:27:56,186 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:27:56,187 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:27:56,188 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:27:56,192 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:27:56,196 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:27:56,196 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:27:56,197 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:27:56,197 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:27:56,198 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:27:56,201 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:27:56,215 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_122756.log
2025-07-30 12:27:56,228 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:27:56,232 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:27:56,233 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:27:56,234 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:27:56,235 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:27:56,236 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:27:56,239 - main_controller - INFO - 📅 当前北京时间: 2025-07-30 20:27:56
2025-07-30 12:27:56,244 - main_controller - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:27:56,246 - main_controller - INFO - 📅 启动时间: 2025-07-30 20:27:56
2025-07-30 12:27:56,249 - main_controller - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:27:56,254 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:27:56,263 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:56,265 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:27:56,267 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1248166, 进程: Weixin.exe)
2025-07-30 12:27:56,268 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:56,269 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:27:56,297 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:27:56,314 - main_controller - INFO - ✅ 找到 3 个可用微信窗口
2025-07-30 12:27:56,317 - main_controller - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 12:27:56,322 - main_controller - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:27:56,328 - main_controller - INFO -   窗口 3: 添加朋友 (句柄: 1248166)
2025-07-30 12:27:56,332 - main_controller - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 12:27:56,332 - main_controller - INFO - 📊 需要移动的窗口数量: 3
2025-07-30 12:27:56,333 - main_controller - INFO - 🔄 移动窗口 1/3: 微信 (句柄: 263568)
2025-07-30 12:27:56,343 - main_controller - INFO - ✅ 窗口 1 移动成功
2025-07-30 12:27:56,646 - main_controller - INFO - 🔄 移动窗口 2/3: 微信 (句柄: 9045874)
2025-07-30 12:27:56,646 - main_controller - INFO - ✅ 窗口 2 移动成功
2025-07-30 12:27:56,947 - main_controller - INFO - 🔄 移动窗口 3/3: 添加朋友 (句柄: 1248166)
2025-07-30 12:27:56,948 - main_controller - INFO - ✅ 窗口 3 移动成功
2025-07-30 12:27:56,948 - main_controller - INFO - 📊 批量窗口移动完成统计:
2025-07-30 12:27:56,949 - main_controller - INFO -   ✅ 成功移动: 3 个窗口
2025-07-30 12:27:56,949 - main_controller - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 12:27:56,949 - main_controller - INFO -   📈 成功率: 100.0%
2025-07-30 12:27:56,949 - main_controller - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 12:27:56,950 - main_controller - INFO - 📂 开始加载联系人数据...
2025-07-30 12:27:58,139 - main_controller - INFO - ✅ 加载联系人数据完成
2025-07-30 12:27:58,140 - main_controller - INFO - 📊 总联系人数: 3135
2025-07-30 12:27:58,141 - main_controller - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:27:58,142 - main_controller - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:27:58,143 - main_controller - INFO - 📊 总窗口数: 3, 总联系人数: 2899
2025-07-30 12:27:58,144 - main_controller - INFO - 
============================================================
2025-07-30 12:27:58,145 - main_controller - INFO - 🎯 开始处理第 1/3 个微信窗口 (第 1 轮)
2025-07-30 12:27:58,145 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:27:58,145 - main_controller - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:27:58,146 - main_controller - INFO - ============================================================
2025-07-30 12:27:58,146 - main_controller - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:27:58,147 - main_controller - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:27:58,147 - main_controller - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:27:58,147 - main_controller - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:27:58,248 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:27:58,249 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:27:58,553 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:27:58,554 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:27:58,554 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:27:58,555 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:27:58,555 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:27:58,556 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:27:58,558 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:27:58,558 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:27:58,559 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:27:58,560 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:27:58,762 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:27:58,763 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:27:58,765 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:27:59,066 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:27:59,066 - main_controller - INFO - ✅ 步骤1：窗口管理完成（窗口已激活，位置已在批量移动阶段设置）
2025-07-30 12:27:59,066 - main_controller - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:28:00,067 - main_controller - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:28:00,068 - main_controller - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 12:28:00,068 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:28:00,068 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:28:00,069 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:28:00,069 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:28:00,069 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:28:00,069 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:28:00,270 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:28:00,271 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:28:02,644 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:28:02,644 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:28:02,645 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 12:28:04,938 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:28:05,139 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:28:05,143 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:28:07,512 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:28:07,512 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:28:07,513 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 12:28:09,640 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:28:09,841 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:28:09,842 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:28:12,226 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:28:12,227 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:28:12,227 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-30 12:28:15,193 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:28:15,394 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:28:15,394 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:28:17,776 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:28:17,776 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:28:17,776 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 12:28:19,444 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:28:19,645 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:28:19,646 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:28:22,026 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:28:22,026 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:28:22,027 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:28:22,027 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:28:22,027 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:28:22,029 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:28:22,029 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:28:22,030 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1248166, 进程: Weixin.exe)
2025-07-30 12:28:22,031 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:28:22,031 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:28:22,033 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:28:22,034 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1248166)
2025-07-30 12:28:22,035 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1248166) - 增强版
2025-07-30 12:28:22,340 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:28:22,342 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:28:22,342 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:28:22,343 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:28:22,344 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 12:28:22,345 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:28:22,345 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 12:28:22,345 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:28:22,549 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:28:22,550 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:28:22,550 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 12:28:22,550 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 12:28:22,550 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 12:28:22,551 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:28:22,551 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 12:28:23,552 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 12:28:23,552 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:28:23,554 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:28:23,554 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:28:23,557 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1248166, 进程: Weixin.exe)
2025-07-30 12:28:23,558 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:28:23,559 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:28:23,561 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:28:23,561 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 12:28:23,562 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 12:28:23,562 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 12:28:23,562 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:28:23,562 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:28:23,871 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:28:23,873 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:28:23,874 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:28:23,874 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:28:23,875 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:28:23,875 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:28:23,876 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:28:23,876 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:28:23,877 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:28:23,877 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:28:24,080 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:28:24,080 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:28:24,083 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:28:24,383 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:28:24,384 - main_controller - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 12:28:24,384 - main_controller - INFO - ✅ 步骤 2 执行成功
2025-07-30 12:28:25,385 - main_controller - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 12:28:25,385 - main_controller - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 12:28:25,385 - main_controller - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 12:28:25,390 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_122825.log
2025-07-30 12:28:25,393 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:28:25,393 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:28:25,394 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:28:25,394 - main_controller - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 12:28:25,394 - main_controller - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 12:28:25,395 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 12:28:25,396 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 12:28:25,397 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 12:28:25,397 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 12:28:25,397 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 12:28:25,398 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 12:28:25,398 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 12:28:25,399 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 12:28:25,400 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:28:25,401 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1248166
2025-07-30 12:28:25,401 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1248166) - 增强版
2025-07-30 12:28:25,715 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:28:25,715 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:28:25,716 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:28:25,716 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:28:25,717 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 12:28:25,718 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:28:25,718 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 12:28:25,719 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:28:25,921 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:28:25,923 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:28:25,928 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1248166 (API返回: None)
2025-07-30 12:28:26,228 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:28:26,229 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 12:28:26,230 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 12:28:26,230 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 12:28:26,231 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:28:26,232 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 12:28:26,232 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 12:28:26,235 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 12:28:26,235 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 12:28:27,037 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 12:28:27,037 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:28:27,540 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2901 个
2025-07-30 12:28:27,542 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2901 个 (总计: 3135 个)
2025-07-30 12:28:27,543 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 12:28:27,544 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 12:28:27,545 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:28:27,545 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:28:27,546 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 12:28:27,546 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2901
2025-07-30 12:28:27,547 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13580982528 (钟建强)
2025-07-30 12:28:27,548 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:28:27,549 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:28:30,636 - main_controller - INFO - ⏹️ 用户中断程序执行
