2025-07-30 15:17:51,585 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:17:51,586 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:17:51,588 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:17:51,589 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:17:51,590 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:17:51,591 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:17:51,593 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:17:51,596 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 15:17:51,598 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:17:51,599 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:17:51,600 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:17:51,600 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:17:51,601 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:17:51,604 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:17:51,607 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_151751.log
2025-07-30 15:17:51,609 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:17:51,610 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:17:51,617 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:17:51,618 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 15:17:51,619 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 15:17:51,619 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 15:17:51,620 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 23:17:51
2025-07-30 15:17:51,620 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 15:17:51,620 - __main__ - INFO - 📅 启动时间: 2025-07-30 23:17:51
2025-07-30 15:17:51,620 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:17:51,621 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:17:51,627 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:17:51,629 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:17:52,159 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:17:52,159 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:17:52,160 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:17:52,160 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:17:52,161 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 15:17:52,161 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 15:17:52,162 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:17:52,162 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:17:52,162 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:17:52,163 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:17:52,163 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 6032430)
2025-07-30 15:17:52,163 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 6621532)
2025-07-30 15:17:52,164 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 15:17:52,164 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 15:17:52,164 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 6032430)
2025-07-30 15:17:52,164 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 15:17:52,465 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 6621532)
2025-07-30 15:17:52,466 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-30 15:17:52,466 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 15:17:52,466 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 15:17:52,466 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 15:17:52,467 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 15:17:52,467 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 15:17:52,467 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 15:17:53,435 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 15:17:53,435 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 15:17:53,436 - __main__ - INFO - 📋 待处理联系人数: 2840
2025-07-30 15:17:53,436 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 15:17:53,437 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2840
2025-07-30 15:17:53,437 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 2
2025-07-30 15:17:53,438 - __main__ - INFO - 
============================================================
2025-07-30 15:17:53,438 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 15:17:53,438 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:17:53,438 - __main__ - INFO - 📊 全局进度：已处理 0/2840 个联系人（剩余 2840 个）
2025-07-30 15:17:53,439 - __main__ - INFO - ============================================================
2025-07-30 15:17:53,439 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:17:53,439 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:17:53,439 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:17:53,440 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:17:53,440 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 6032430)
2025-07-30 15:17:53,440 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:17:54,241 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:17:54,241 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6032430
2025-07-30 15:17:54,241 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:17:54,546 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:17:54,546 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:17:54,547 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:17:54,547 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:17:54,548 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:17:54,548 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:17:54,549 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:17:54,549 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:17:54,550 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:17:54,550 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:17:54,752 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:17:54,752 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:17:54,757 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6032430 (API返回: None)
2025-07-30 15:17:55,058 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:17:55,058 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:17:55,058 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:17:56,059 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:17:56,059 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:17:56,060 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:17:56,060 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:17:56,060 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:17:56,063 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:17:56,063 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:17:56,063 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:17:56,064 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:17:56,265 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:17:56,265 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:17:56,267 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:17:58,648 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:17:58,648 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:17:58,649 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 15:18:01,288 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:18:01,489 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:18:01,490 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:18:01,490 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:18:03,863 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:18:03,863 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:18:03,864 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 15:18:06,151 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:18:06,352 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:18:06,352 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:18:06,353 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:18:08,731 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:18:08,731 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:18:08,732 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 15:18:10,954 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:18:11,155 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:18:11,156 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:18:11,156 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:18:13,531 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:18:13,532 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:18:13,532 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 15:18:15,367 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:18:15,568 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:18:15,569 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:18:15,569 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:18:17,947 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:18:17,948 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:18:17,948 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:18:17,948 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:18:17,949 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:18:17,950 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:18:17,951 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:18:17,951 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 10618270, 进程: Weixin.exe)
2025-07-30 15:18:17,952 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:18:17,953 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:18:17,956 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:18:17,957 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 10618270)
2025-07-30 15:18:17,957 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 10618270) - 增强版
2025-07-30 15:18:18,261 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:18:18,262 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:18:18,262 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:18:18,263 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:18:18,263 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:18:18,263 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:18:18,468 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:18:18,469 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:18:18,671 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:18:18,671 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:18:18,672 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:18:18,672 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:18:18,672 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:18:18,673 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:18:18,673 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:18:19,673 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:18:19,674 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:18:19,675 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:18:19,677 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:18:19,678 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 10618270, 进程: Weixin.exe)
2025-07-30 15:18:19,679 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:18:19,680 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:18:19,682 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:18:19,683 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:18:19,683 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:18:19,683 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:18:19,684 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:18:19,684 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6032430
2025-07-30 15:18:19,684 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:18:19,993 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:18:19,993 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:18:19,994 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:18:19,994 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:18:19,994 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:18:19,995 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:18:19,995 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:18:19,995 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:18:19,996 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:18:19,996 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:18:20,197 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:18:20,198 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:18:20,200 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6032430 (API返回: None)
2025-07-30 15:18:20,501 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:18:20,501 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:18:20,502 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:18:20,502 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:18:21,502 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:18:21,503 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:18:21,503 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:18:21,505 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_151821.log
2025-07-30 15:18:21,506 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:18:21,507 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:18:21,507 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:18:21,507 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 15:18:21,508 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:18:21,508 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:18:21,512 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 15:18:21,512 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:18:21,513 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 15:18:21,513 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:18:21,514 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 15:18:21,515 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 15:18:21,515 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:18:21,516 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:18:21,517 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 10618270
2025-07-30 15:18:21,517 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 10618270) - 增强版
2025-07-30 15:18:21,828 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:18:21,828 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:18:21,829 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:18:21,830 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:18:21,830 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:18:21,830 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:18:21,831 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:18:21,831 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:18:22,033 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:18:22,033 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:18:22,038 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 10618270 (API返回: None)
2025-07-30 15:18:22,339 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:18:22,339 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:18:22,340 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:18:22,340 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:18:22,341 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:18:22,341 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:18:22,341 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:18:22,346 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:18:22,346 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:18:22,907 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:18:22,908 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:18:23,192 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2840 个
2025-07-30 15:18:23,193 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2840 个 (总计: 3135 个)
2025-07-30 15:18:23,194 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:18:23,195 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:18:23,195 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:18:23,196 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:18:23,198 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:18:23,198 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:18:23,199 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2840
2025-07-30 15:18:23,200 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18681482191 (朱文茜)
2025-07-30 15:18:23,201 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:18:23,201 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:18:23,203 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:18:29,802 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18681482191
2025-07-30 15:18:29,802 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:18:29,803 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18681482191 执行添加朋友操作...
2025-07-30 15:18:29,803 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:18:29,803 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:18:29,804 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:18:29,805 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:18:29,814 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:18:29,817 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:18:29,818 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:18:29,819 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:18:29,820 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:18:29,822 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:18:29,824 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:18:29,825 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:18:29,837 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:18:29,839 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:18:29,841 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:18:29,849 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:18:29,854 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 15:18:29,857 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:18:30,364 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:18:30,365 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:18:30,437 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.43, 边缘比例0.0378
2025-07-30 15:18:30,444 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_151830.png
2025-07-30 15:18:30,446 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:18:30,448 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:18:30,450 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:18:30,451 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:18:30,452 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:18:30,456 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_151830.png
2025-07-30 15:18:30,457 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:18:30,459 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:18:30,461 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:18:30,463 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:18:30,466 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:18:30,472 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:18:30,474 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:18:30,477 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:18:30,487 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_151830.png
2025-07-30 15:18:30,488 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:18:30,489 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:18:30,493 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_151830.png
2025-07-30 15:18:30,566 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:18:30,568 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:18:30,569 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:18:30,570 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:18:30,871 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:18:31,679 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:18:31,680 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:18:31,681 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:18:31,682 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:18:31,684 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:18:31,684 - modules.wechat_auto_add_simple - INFO - ✅ 18681482191 添加朋友操作执行成功
2025-07-30 15:18:31,685 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:18:31,687 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:18:31,689 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:18:31,689 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:18:33,691 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:18:33,692 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:18:33,693 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:18:33,693 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:18:33,693 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:18:33,694 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:18:33,694 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:18:33,695 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:18:33,695 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:18:33,695 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18681482191
2025-07-30 15:18:33,699 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:18:33,700 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:18:33,700 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:18:33,701 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:18:33,701 - modules.friend_request_window - INFO -    📱 phone: '18681482191'
2025-07-30 15:18:33,703 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:18:33,703 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:18:34,258 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:18:34,259 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:18:34,259 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:18:34,260 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:18:34,262 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18681482191
2025-07-30 15:18:34,262 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:18:34,263 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:18:34,266 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:18:34,267 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:18:34,267 - modules.friend_request_window - INFO -    📱 手机号码: 18681482191
2025-07-30 15:18:34,268 - modules.friend_request_window - INFO -    🆔 准考证: 014325120008
2025-07-30 15:18:34,268 - modules.friend_request_window - INFO -    👤 姓名: 朱文茜
2025-07-30 15:18:34,268 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:18:34,269 - modules.friend_request_window - INFO -    📝 备注格式: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:34,269 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:18:34,269 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:34,270 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:18:34,271 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3672514, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:18:34,273 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3672514)
2025-07-30 15:18:34,274 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:18:34,275 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:18:34,276 - modules.friend_request_window - INFO - 🔄 激活窗口: 3672514
2025-07-30 15:18:34,979 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:18:34,980 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:18:34,981 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:18:34,981 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:18:34,981 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:18:34,982 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:18:34,982 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:18:34,983 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:18:34,984 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:18:34,984 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:18:34,985 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:18:34,986 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:18:34,986 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:18:34,986 - modules.friend_request_window - INFO -    📝 remark参数: '014325120008-朱文茜-2025-07-30 23:18:34' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:18:34,987 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:18:34,987 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:34,988 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:18:34,988 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:18:34,988 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:18:34,989 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:18:34,989 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:18:34,989 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:18:34,989 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:18:35,895 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:18:41,138 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:18:41,139 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:18:41,139 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:18:41,139 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:18:41,142 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 15:00:31,626 - modules.window_manager -...' (前50字符)
2025-07-30 15:18:41,455 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:18:41,455 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:18:42,359 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:18:42,369 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:18:42,369 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:18:42,370 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:18:42,371 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:18:42,372 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:18:42,873 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:18:42,874 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:18:42,874 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:18:42,875 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:18:42,875 - modules.friend_request_window - INFO -    📝 内容: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:42,875 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:18:42,875 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120008-\xe6\x9c\xb1\xe6\x96\x87\xe8\x8c\x9c-2025-07-30 23:18:34'
2025-07-30 15:18:42,876 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:18:43,795 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:18:49,038 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:18:49,039 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:18:49,039 - modules.friend_request_window - INFO -    📝 原始文本: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:49,040 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:18:49,041 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 15:00:31,626 - modules.window_manager -...' (前50字符)
2025-07-30 15:18:49,354 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:18:49,354 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:18:50,257 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:18:50,267 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:18:50,268 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:50,269 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:18:50,270 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:50,271 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:18:50,772 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120008-朱文茜-2025-07-30 23:18:34'
2025-07-30 15:18:50,772 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:18:50,772 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:18:50,773 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:18:50,773 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:18:50,773 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:18:50,774 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:18:51,575 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:18:51,576 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:18:51,576 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:18:52,195 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:18:52,196 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:18:52,198 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:18:52,198 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:18:52,198 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:18:52,199 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:18:52,700 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:18:52,702 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:18:52,702 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:18:52,703 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:18:52,703 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:18:52,703 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:18:52,703 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:18:52,704 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:18:52,704 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:18:52,704 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:18:52,705 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:18:52,706 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:18:52,709 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:18:52,710 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:18:52,711 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:18:52,711 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:18:52,712 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:18:52,714 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 3
2025-07-30 15:18:52,714 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 15:18:52,715 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 12716850)
2025-07-30 15:18:52,719 - modules.friend_request_window - INFO - ✅ 频率错误窗口已强制添加到黑名单
2025-07-30 15:18:52,719 - modules.friend_request_window - INFO - 📊 当前黑名单窗口数量: 1
2025-07-30 15:18:52,721 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 15:18:52,722 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 15:18:53,225 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 15:18:53,226 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 15:18:53,226 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 15:18:53,226 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 15:18:53,227 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 15:18:53,227 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 15:18:53,227 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 15:18:53,228 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 15:18:54,164 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 15:18:54,164 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 15:18:54,165 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 15:18:54,165 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 15:18:54,186 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 15:18:54,187 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 15:18:54,987 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 15:18:54,988 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 15:18:54,988 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 15:18:54,988 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 15:18:54,989 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 15:18:54,989 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 15:18:54,989 - modules.friend_request_window - INFO - 🎯 强制使用坐标(700, 16)关闭微信主窗口
2025-07-30 15:18:54,990 - modules.friend_request_window - INFO -    激活窗口...
2025-07-30 15:18:55,490 - modules.friend_request_window - INFO -    使用坐标(700, 16)点击关闭按钮...
2025-07-30 15:18:57,123 - modules.friend_request_window - INFO - ✅ 第一次坐标点击成功关闭微信主窗口
2025-07-30 15:18:57,123 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 15:18:57,124 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 15:18:57,124 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 15:18:59,125 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 15:18:59,126 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:18:59,127 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 15:18:59,127 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:18:59,129 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:18:59,129 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:18:59,131 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:18:59,131 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 15:18:59,131 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 15:18:59,156 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 15:18:59,157 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 15:18:59,158 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 15:18:59,159 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 6032430
2025-07-30 15:18:59,160 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:18:59,161 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:18:59,163 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 15:18:59,701 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:18:59,769 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:18:59,791 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:18:59,806 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:18:59,813 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:18:59,819 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:18:59,824 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:18:59,827 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:18:59,829 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:18:59,831 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:19:00,033 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:19:00,043 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:19:00,044 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 15:19:00,045 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 15:19:00,046 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 15:19:00,046 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 15:19:00,047 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 15:19:02,064 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 15:19:02,066 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:19:02,066 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:19:02,066 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 15:19:02,066 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 15:19:02,067 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 15:19:02,067 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:19:02,067 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:19:02,068 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:19:02,068 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:19:02,068 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:19:02,068 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:19:02,269 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:19:02,269 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:19:02,270 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:19:04,803 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:19:04,803 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:19:04,804 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
