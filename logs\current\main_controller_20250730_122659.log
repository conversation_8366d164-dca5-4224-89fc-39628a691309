2025-07-30 12:26:59,926 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:26:59,928 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:26:59,929 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:26:59,929 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:26:59,930 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:26:59,930 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:26:59,932 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:26:59,933 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:26:59,934 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:26:59,935 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:26:59,936 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:26:59,937 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:26:59,940 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:26:59,943 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_122659.log
2025-07-30 12:26:59,945 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:26:59,945 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:26:59,946 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:26:59,947 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:26:59,947 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:26:59,948 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:26:59,948 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 20:26:59
2025-07-30 12:26:59,950 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:26:59,951 - __main__ - INFO - 📅 启动时间: 2025-07-30 20:26:59
2025-07-30 12:26:59,951 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:26:59,952 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:27:00,517 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:00,630 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:27:01,183 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:01,184 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:27:01,184 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:27:01,185 - __main__ - INFO - ✅ 找到 2 个可用微信窗口
2025-07-30 12:27:01,185 - __main__ - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 12:27:01,185 - __main__ - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:27:01,185 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 12:27:01,186 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 12:27:01,186 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 263568)
2025-07-30 12:27:01,188 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 12:27:01,189 - __main__ - WARNING - ⚠️ 窗口 1 移动失败
2025-07-30 12:27:01,489 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 9045874)
2025-07-30 12:27:01,492 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 12:27:01,495 - __main__ - WARNING - ⚠️ 窗口 2 移动失败
2025-07-30 12:27:01,496 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 12:27:01,497 - __main__ - INFO -   ✅ 成功移动: 0 个窗口
2025-07-30 12:27:01,497 - __main__ - INFO -   ❌ 移动失败: 2 个窗口
2025-07-30 12:27:01,498 - __main__ - INFO -   📈 成功率: 0.0%
2025-07-30 12:27:01,498 - __main__ - ERROR - ❌ 所有窗口移动都失败了
2025-07-30 12:27:01,498 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 12:27:02,546 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 12:27:02,546 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 12:27:02,547 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:27:02,547 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:27:02,547 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 12:27:02,548 - __main__ - INFO - 
============================================================
2025-07-30 12:27:02,548 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 12:27:02,548 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:27:02,549 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:27:02,549 - __main__ - INFO - ============================================================
2025-07-30 12:27:02,549 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:27:02,550 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:27:02,550 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:27:02,550 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:27:02,652 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:27:02,652 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:27:02,957 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:27:02,957 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:27:02,957 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:27:02,958 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:27:02,958 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:27:02,959 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:27:02,960 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:27:02,961 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:27:02,961 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:27:02,962 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:27:03,164 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:27:03,164 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:27:03,167 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:27:03,468 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:27:03,468 - __main__ - INFO - ✅ 步骤1：窗口管理完成（窗口已激活，位置已在批量移动阶段设置）
2025-07-30 12:27:03,469 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:27:04,469 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:27:04,470 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 12:27:04,471 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:27:04,472 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:27:04,473 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:27:04,475 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:27:04,480 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:27:04,481 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:27:04,682 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:27:04,683 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:27:07,063 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:27:07,064 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:27:07,065 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 12:27:08,738 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:27:08,939 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:27:08,940 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:27:11,308 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:27:11,308 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:27:11,308 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 12:27:14,191 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:27:14,393 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:27:14,394 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:27:16,781 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:27:16,782 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:27:16,782 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 12:27:18,415 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:27:18,633 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:27:18,653 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:27:21,030 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:27:21,030 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:27:21,031 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 12:27:23,356 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:27:23,557 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:27:23,557 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:27:25,946 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:27:25,946 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:27:25,947 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:27:25,947 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:27:25,947 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:27:25,949 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:25,949 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:27:25,950 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1248166, 进程: Weixin.exe)
2025-07-30 12:27:25,951 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:25,951 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:27:25,954 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:27:25,955 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1248166)
2025-07-30 12:27:25,956 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1248166) - 增强版
2025-07-30 12:27:26,260 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:27:26,261 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:27:26,261 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:27:26,262 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:27:26,262 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 12:27:26,262 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:27:26,466 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 12:27:26,466 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:27:26,668 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:27:26,668 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:27:26,669 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 12:27:26,669 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 12:27:26,669 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 12:27:26,669 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:27:26,670 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 12:27:27,670 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 12:27:27,671 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:27:27,672 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:27,673 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:27:27,674 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1248166, 进程: Weixin.exe)
2025-07-30 12:27:27,676 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:27:27,678 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:27:27,680 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:27:27,681 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 12:27:27,681 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 12:27:27,681 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 12:27:27,682 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:27:27,682 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:27:27,990 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:27:27,991 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:27:27,991 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:27:27,993 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:27:27,993 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:27:27,994 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:27:27,995 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:27:27,995 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:27:27,995 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:27:27,996 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:27:28,198 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:27:28,209 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:27:28,210 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:27:28,527 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:27:28,527 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 12:27:28,528 - __main__ - INFO - ✅ 步骤 2 执行成功
