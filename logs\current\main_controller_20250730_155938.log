2025-07-30 15:59:38,412 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:59:38,412 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:59:38,414 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:59:38,415 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-30 15:59:38,416 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-07-30 15:59:38,419 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-30 15:59:38,422 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:59:38,426 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:59:38,429 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:59:38,450 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:59:38,454 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 15:59:38,454 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:59:38,455 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:59:38,461 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:59:38,462 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:59:38,464 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:59:38,476 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:59:38,521 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_155938.log
2025-07-30 15:59:38,528 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:59:38,535 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:59:38,535 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:59:38,536 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 15:59:38,536 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 15:59:38,537 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 15:59:38,537 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 23:59:38
2025-07-30 15:59:38,538 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 15:59:38,549 - __main__ - INFO - 📅 启动时间: 2025-07-30 23:59:38
2025-07-30 15:59:38,549 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:59:38,550 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:59:39,096 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:59:39,098 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:59:39,101 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:59:39,101 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:59:39,102 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 15:59:39,102 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 15:59:39,103 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:59:39,103 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:59:39,104 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:59:39,105 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:59:39,112 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:59:39,122 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 15:59:39,137 - __main__ - INFO - 📊 需要移动的窗口数量: 1
2025-07-30 15:59:39,139 - __main__ - INFO - 🔄 移动窗口 1/1: 微信 (句柄: 2426918)
2025-07-30 15:59:39,144 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 15:59:39,153 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 15:59:39,154 - __main__ - INFO -   ✅ 成功移动: 1 个窗口
2025-07-30 15:59:39,155 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 15:59:39,155 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 15:59:39,155 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 15:59:39,162 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 15:59:40,782 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 15:59:40,783 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 15:59:40,783 - __main__ - INFO - 📋 待处理联系人数: 2831
2025-07-30 15:59:40,784 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 15:59:40,784 - __main__ - INFO - 📊 总窗口数: 1, 总联系人数: 2831
2025-07-30 15:59:40,784 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 1
2025-07-30 15:59:40,785 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:59:40,786 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:59:40,787 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:59:40,788 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:59:40,804 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:59:40,805 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:59:40,805 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 15:59:40,806 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 15:59:40,813 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:59:40,819 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:59:40,821 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:59:40,831 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:59:40,835 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:59:40,836 - __main__ - INFO - 
============================================================
2025-07-30 15:59:40,837 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 1 轮)
2025-07-30 15:59:40,837 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:59:40,837 - __main__ - INFO - 📊 全局进度：已处理 0/2831 个联系人（剩余 2831 个）
2025-07-30 15:59:40,838 - __main__ - INFO - ============================================================
2025-07-30 15:59:40,838 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:59:40,852 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:59:40,853 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:59:40,853 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:59:40,854 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2426918)
2025-07-30 15:59:40,855 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-30 15:59:40,856 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 15:59:41,864 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:59:42,967 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:59:42,968 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 15:59:42,976 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 15:59:43,292 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:59:43,293 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:59:43,294 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:59:43,295 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:59:43,295 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:59:43,296 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:59:43,297 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:59:43,298 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:59:43,300 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:59:43,321 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:59:43,525 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:59:43,525 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:59:43,527 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 15:59:43,829 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:59:43,830 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:59:43,830 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:59:44,831 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:59:44,831 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:59:44,832 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:59:44,832 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:59:44,833 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:59:44,833 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:59:44,833 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:59:44,834 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:59:44,834 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:59:45,035 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:59:45,036 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:59:45,037 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:59:47,427 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:59:47,428 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:59:47,428 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 15:59:49,524 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:59:49,726 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:59:49,727 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:59:49,727 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:59:52,110 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:59:52,114 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:59:52,115 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 15:59:54,560 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:59:54,764 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:59:54,765 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:59:54,766 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:59:57,175 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:59:57,176 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:59:57,176 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 15:59:58,995 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:59:59,195 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:59:59,196 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:59:59,196 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 16:00:01,575 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 16:00:01,576 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 16:00:01,576 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 16:00:04,012 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 16:00:04,218 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 16:00:04,220 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 16:00:04,221 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 16:00:06,608 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 16:00:06,609 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 16:00:06,610 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 16:00:06,610 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 16:00:06,611 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:00:06,613 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:00:06,613 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:00:06,615 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1312236, 进程: Weixin.exe)
2025-07-30 16:00:06,619 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:00:06,620 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1312236)
2025-07-30 16:00:06,621 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1312236) - 增强版
2025-07-30 16:00:06,929 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:00:06,930 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:00:06,930 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 16:00:06,931 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 16:00:06,931 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 16:00:06,932 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 16:00:07,141 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 16:00:07,142 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 16:00:07,344 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:00:07,345 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:00:07,345 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 16:00:07,345 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 16:00:07,346 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 16:00:07,346 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 16:00:07,346 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 16:00:08,347 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 16:00:08,347 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:00:08,349 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:00:08,349 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:00:08,350 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1312236, 进程: Weixin.exe)
2025-07-30 16:00:08,352 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:00:08,352 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 16:00:08,353 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 16:00:08,354 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 16:00:08,354 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 16:00:08,354 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 16:00:08,356 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 16:00:08,663 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:00:08,664 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:00:08,664 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 16:00:08,664 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 16:00:08,665 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 16:00:08,665 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 16:00:08,665 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 16:00:08,666 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 16:00:08,666 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 16:00:08,667 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 16:00:08,870 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:00:08,870 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:00:08,873 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 16:00:09,173 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:00:09,174 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 16:00:09,175 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 16:00:09,176 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 16:00:10,176 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 16:00:10,177 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 16:00:10,177 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 16:00:10,179 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_160010.log
2025-07-30 16:00:10,180 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:00:10,181 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 16:00:10,181 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 16:00:10,181 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 16:00:10,181 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 16:00:10,182 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 16:00:10,184 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 16:00:10,184 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 16:00:10,185 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 16:00:10,185 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 16:00:10,185 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 16:00:10,185 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 16:00:10,186 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:00:10,187 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1312236
2025-07-30 16:00:10,187 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1312236) - 增强版
2025-07-30 16:00:10,497 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:00:10,497 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:00:10,498 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 16:00:10,498 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 16:00:10,499 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 16:00:10,499 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 16:00:10,499 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 16:00:10,499 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 16:00:10,701 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:00:10,701 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:00:10,706 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1312236 (API返回: None)
2025-07-30 16:00:11,006 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:00:11,006 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 16:00:11,007 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 16:00:11,007 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 16:00:11,008 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:00:11,009 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 16:00:11,009 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 16:00:11,016 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 16:00:11,016 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 16:00:11,595 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 16:00:11,595 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:00:11,890 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2831 个
2025-07-30 16:00:11,891 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2831 个 (总计: 3135 个)
2025-07-30 16:00:11,891 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 16:00:11,891 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 16:00:11,892 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:11,892 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:11,893 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:11,894 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:11,894 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:00:11,895 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 16:00:11,895 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2831
2025-07-30 16:00:11,895 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15972744355 (鲁臻婧)
2025-07-30 16:00:11,896 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:11,897 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:11,898 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:11,899 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:11,899 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:00:18,511 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15972744355
2025-07-30 16:00:18,511 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 16:00:18,512 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15972744355 执行添加朋友操作...
2025-07-30 16:00:18,512 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 16:00:18,513 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 16:00:18,514 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 16:00:18,515 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 16:00:18,516 - WeChatAutoAdd - INFO - screenshots目录已经是干净的
2025-07-30 16:00:18,516 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 16:00:18,517 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 16:00:18,518 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 16:00:18,518 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 16:00:18,518 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 16:00:18,519 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 16:00:18,519 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 16:00:18,525 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 16:00:18,535 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 16:00:18,541 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 16:00:18,548 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 16:00:18,553 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 16:00:19,061 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 16:00:19,062 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 16:00:19,130 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 16:00:19,131 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 16:00:19,139 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_160019.png
2025-07-30 16:00:19,140 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 16:00:19,142 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 16:00:19,143 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 16:00:19,144 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 16:00:19,148 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 16:00:19,155 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_160019.png
2025-07-30 16:00:19,163 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-07-30 16:00:19,164 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,453), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 16:00:19,165 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 16:00:19,169 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 16:00:19,176 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 16:00:19,182 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 16:00:19,184 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 16:00:19,186 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 16:00:19,195 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 16:00:19,205 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_160019.png
2025-07-30 16:00:19,212 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 16:00:19,214 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 16:00:19,233 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_160019.png
2025-07-30 16:00:19,290 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 16:00:19,293 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 16:00:19,293 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 16:00:19,294 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 16:00:19,595 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 16:00:20,374 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 16:00:20,375 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 16:00:20,376 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:20,377 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:20,378 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:20,381 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:20,381 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:00:20,382 - modules.wechat_auto_add_simple - INFO - ✅ 15972744355 添加朋友操作执行成功
2025-07-30 16:00:20,382 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:20,382 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:20,384 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:20,385 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:20,385 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:00:20,385 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 16:00:22,387 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 16:00:22,388 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 16:00:22,388 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 16:00:22,389 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 16:00:22,389 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 16:00:22,389 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 16:00:22,390 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 16:00:22,390 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 16:00:22,391 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 16:00:22,391 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15972744355
2025-07-30 16:00:22,396 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 16:00:22,396 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 16:00:22,397 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 16:00:22,398 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 16:00:22,399 - modules.friend_request_window - INFO -    📱 phone: '15972744355'
2025-07-30 16:00:22,400 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 16:00:22,401 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 16:00:22,989 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 16:00:22,989 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 16:00:22,990 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 16:00:22,990 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:00:22,992 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15972744355
2025-07-30 16:00:22,993 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 16:00:22,994 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:00:22,995 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 16:00:22,996 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 16:00:22,997 - modules.friend_request_window - INFO -    📱 手机号码: 15972744355
2025-07-30 16:00:22,998 - modules.friend_request_window - INFO -    🆔 准考证: 014425110017
2025-07-30 16:00:22,998 - modules.friend_request_window - INFO -    👤 姓名: 鲁臻婧
2025-07-30 16:00:22,998 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 16:00:22,998 - modules.friend_request_window - INFO -    📝 备注格式: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:22,999 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:00:22,999 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:22,999 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 16:00:23,000 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2950916, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 16:00:23,001 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2950916)
2025-07-30 16:00:23,002 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 16:00:23,002 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 16:00:23,003 - modules.friend_request_window - INFO - 🔄 激活窗口: 2950916
2025-07-30 16:00:23,708 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 16:00:23,709 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 16:00:23,709 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 16:00:23,710 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 16:00:23,710 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 16:00:23,711 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 16:00:23,711 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 16:00:23,711 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 16:00:23,711 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 16:00:23,712 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 16:00:23,712 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 16:00:23,712 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 16:00:23,712 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 16:00:23,713 - modules.friend_request_window - INFO -    📝 remark参数: '014425110017-鲁臻婧-2025-07-31 00:00:22' (类型: <class 'str'>, 长度: 36)
2025-07-30 16:00:23,713 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 16:00:23,713 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:23,714 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 16:00:23,715 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 16:00:23,715 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 16:00:23,716 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 16:00:23,716 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 16:00:23,717 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 16:00:23,717 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 16:00:24,624 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 16:00:29,869 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 16:00:29,870 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 16:00:29,870 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 16:00:29,871 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 16:00:29,873 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 16:00:30,187 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 16:00:30,187 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 16:00:31,090 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 16:00:31,099 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 16:00:31,099 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 16:00:31,100 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 16:00:31,100 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 16:00:31,100 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 16:00:31,601 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 16:00:31,601 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 16:00:31,602 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 16:00:31,602 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 16:00:31,602 - modules.friend_request_window - INFO -    📝 内容: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:31,603 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 16:00:31,603 - modules.friend_request_window - INFO -    🔤 内容编码: b'014425110017-\xe9\xb2\x81\xe8\x87\xbb\xe5\xa9\xa7-2025-07-31 00:00:22'
2025-07-30 16:00:31,604 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 16:00:32,525 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 16:00:37,771 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 16:00:37,772 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 16:00:37,772 - modules.friend_request_window - INFO -    📝 原始文本: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:37,773 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 16:00:37,773 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 16:00:38,086 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 16:00:38,088 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 16:00:38,992 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 16:00:39,002 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 16:00:39,002 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:39,006 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 16:00:39,006 - modules.friend_request_window - INFO -    📝 已填写内容: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:39,006 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 16:00:39,507 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:39,508 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 16:00:39,508 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 16:00:39,508 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 16:00:39,509 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 16:00:39,509 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 16:00:39,509 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 16:00:40,310 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 16:00:40,310 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 16:00:40,311 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 16:00:40,925 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:40,925 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:40,927 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:40,929 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:40,930 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:00:40,933 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 16:00:40,937 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 16:00:40,940 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 16:00:41,443 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 16:00:41,446 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 16:00:41,447 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 16:00:41,447 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 16:00:41,447 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 16:00:41,448 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 16:00:41,448 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 16:00:41,448 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 16:00:41,449 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 16:00:41,450 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 16:00:41,450 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 16:00:41,451 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 16:00:41,451 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 16:00:41,451 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 16:00:41,452 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 16:00:41,452 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 16:00:41,461 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 16:00:41,463 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 16:00:41,465 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:41,472 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:41,474 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 1
2025-07-30 16:00:41,475 - modules.friend_request_window - ERROR - 🚨 检测到单窗口环境且出现频率错误
2025-07-30 16:00:41,477 - modules.friend_request_window - ERROR - 🚨 单窗口环境下无法进行窗口切换，程序必须终止
2025-07-30 16:00:41,477 - modules.friend_request_window - ERROR - 💡 建议：启动多个微信窗口以支持自动切换功能
2025-07-30 16:00:41,478 - modules.friend_request_window - ERROR - 🚨 已设置程序终止标志，主程序将停止执行
2025-07-30 16:00:41,479 - modules.friend_request_window - ERROR - ❌ 所有频率错误处理方式都失败
2025-07-30 16:00:42,480 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 16:00:42,483 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 16:00:42,484 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 16:00:42,484 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 16:00:42,484 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 16:00:42,485 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 16:00:42,485 - modules.friend_request_window - INFO -    📝 备注信息: '014425110017-鲁臻婧-2025-07-31 00:00:22'
2025-07-30 16:00:42,987 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 16:00:42,988 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:42,989 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:42,990 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 16:00:42,990 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:42,992 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:42,992 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:00:42,993 - modules.wechat_auto_add_simple - INFO - ✅ 15972744355 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 16:00:42,994 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15972744355
2025-07-30 16:00:42,996 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:42,997 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:42,999 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 16:00:43,001 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:43,003 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:43,004 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:00:46,725 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2831
2025-07-30 16:00:46,726 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 16722368824 (闫如高)
2025-07-30 16:00:46,726 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:00:46,726 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:00:46,727 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 16:00:46,728 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:00:46,729 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:00:46,730 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
