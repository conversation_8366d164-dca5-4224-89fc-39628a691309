2025-07-30 15:00:25,838 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:00:25,838 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:00:25,840 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 15:00:25,841 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:00:25,843 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:00:25,843 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:00:25,845 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:00:25,847 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 15:00:25,847 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:00:25,848 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:00:25,849 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:00:25,849 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:00:25,850 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:00:25,853 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:00:25,856 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_150025.log
2025-07-30 15:00:25,861 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:00:25,863 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:00:25,864 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:00:25,865 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 15:00:25,866 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 15:00:25,867 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 15:00:25,867 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 23:00:25
2025-07-30 15:00:25,868 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 15:00:25,869 - __main__ - INFO - 📅 启动时间: 2025-07-30 23:00:25
2025-07-30 15:00:25,870 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:00:25,871 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:00:26,412 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:26,412 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:00:26,950 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:26,950 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1183578, 进程: Weixin.exe)
2025-07-30 15:00:27,477 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:27,477 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:00:27,478 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:00:27,479 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:00:27,479 - __main__ - INFO -   🔍 总发现窗口: 3
2025-07-30 15:00:27,479 - __main__ - INFO -   ✅ 有效可用窗口: 3
2025-07-30 15:00:27,479 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:00:27,480 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:00:27,480 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:00:27,480 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:00:27,480 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 1183578)
2025-07-30 15:00:27,481 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 6032430)
2025-07-30 15:00:27,481 - __main__ - INFO -   🔥 窗口 3: 微信 (句柄: 6621532)
2025-07-30 15:00:27,481 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 15:00:27,481 - __main__ - INFO - 📊 需要移动的窗口数量: 3
2025-07-30 15:00:27,481 - __main__ - INFO - 🔄 移动窗口 1/3: 微信 (句柄: 1183578)
2025-07-30 15:00:27,482 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 15:00:27,782 - __main__ - INFO - 🔄 移动窗口 2/3: 微信 (句柄: 6032430)
2025-07-30 15:00:27,785 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 15:00:27,786 - __main__ - WARNING - ⚠️ 窗口 2 移动失败
2025-07-30 15:00:28,087 - __main__ - INFO - 🔄 移动窗口 3/3: 微信 (句柄: 6621532)
2025-07-30 15:00:28,088 - __main__ - INFO - ✅ 窗口 3 移动成功
2025-07-30 15:00:28,088 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 15:00:28,088 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 15:00:28,089 - __main__ - INFO -   ❌ 移动失败: 1 个窗口
2025-07-30 15:00:28,089 - __main__ - INFO -   📈 成功率: 66.7%
2025-07-30 15:00:28,089 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 15:00:28,089 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 15:00:29,208 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 15:00:29,209 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 15:00:29,210 - __main__ - INFO - 📋 待处理联系人数: 2840
2025-07-30 15:00:29,211 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 15:00:29,211 - __main__ - INFO - 📊 总窗口数: 3, 总联系人数: 2840
2025-07-30 15:00:29,212 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 3
2025-07-30 15:00:29,212 - __main__ - INFO - 
============================================================
2025-07-30 15:00:29,213 - __main__ - INFO - 🎯 开始处理第 1/3 个微信窗口 (第 1 轮)
2025-07-30 15:00:29,213 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1183578)
2025-07-30 15:00:29,213 - __main__ - INFO - 📊 全局进度：已处理 0/2840 个联系人（剩余 2840 个）
2025-07-30 15:00:29,214 - __main__ - INFO - ============================================================
2025-07-30 15:00:29,214 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:00:29,215 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1183578)
2025-07-30 15:00:29,216 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:00:29,216 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:00:29,216 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 1183578)
2025-07-30 15:00:29,217 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-07-30 15:00:29,217 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 15:00:30,217 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:00:31,320 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:00:31,320 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1183578
2025-07-30 15:00:31,320 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1183578) - 增强版
2025-07-30 15:00:31,625 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:00:31,625 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:00:31,626 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:00:31,626 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:00:31,626 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:00:31,627 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:00:31,627 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:00:31,628 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:00:31,628 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:00:31,628 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:00:31,830 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:00:31,831 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:00:31,833 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1183578 (API返回: None)
2025-07-30 15:00:32,133 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:00:32,134 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:00:32,134 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:00:33,135 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:00:33,135 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:00:33,135 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:00:33,136 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:00:33,136 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:00:33,137 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:00:33,137 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:00:33,137 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:00:33,137 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:00:33,338 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:00:33,339 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:00:33,340 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:00:35,712 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:00:35,713 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:00:35,714 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 15:00:37,781 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:00:37,982 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:00:37,982 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:00:37,983 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:00:40,363 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:00:40,363 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:00:40,363 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 15:00:42,428 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:00:42,629 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:00:42,629 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:00:42,630 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:00:45,011 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:00:45,011 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:00:45,012 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 15:00:47,085 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:00:47,286 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:00:47,286 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:00:47,287 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:00:49,654 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:00:49,655 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:00:49,655 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 15:00:52,222 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:00:52,423 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:00:52,424 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:00:52,424 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:00:54,793 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:00:54,794 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:00:54,794 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:00:54,794 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:00:54,795 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:00:54,797 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:54,798 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1183578, 进程: Weixin.exe)
2025-07-30 15:00:54,798 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:54,799 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:00:54,800 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2559444, 进程: Weixin.exe)
2025-07-30 15:00:54,801 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:54,802 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:00:54,804 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 15:00:54,804 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 2559444)
2025-07-30 15:00:54,805 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2559444) - 增强版
2025-07-30 15:00:55,109 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:00:55,110 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:00:55,110 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:00:55,110 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:00:55,111 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:00:55,111 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:00:55,315 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:00:55,315 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:00:55,518 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:00:55,518 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:00:55,518 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:00:55,519 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:00:55,519 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:00:55,519 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:00:55,519 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:00:56,520 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:00:56,520 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:00:56,522 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:56,522 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1183578, 进程: Weixin.exe)
2025-07-30 15:00:56,523 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:56,524 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:00:56,526 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2559444, 进程: Weixin.exe)
2025-07-30 15:00:56,527 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:00:56,527 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:00:56,529 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 15:00:56,530 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:00:56,530 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:00:56,530 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:00:56,531 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:00:56,531 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1183578
2025-07-30 15:00:56,531 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1183578) - 增强版
2025-07-30 15:00:56,838 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:00:56,839 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:00:56,839 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:00:56,839 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:00:56,840 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:00:56,840 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:00:56,840 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:00:56,841 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:00:56,841 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:00:56,841 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:00:57,044 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:00:57,044 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:00:57,046 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1183578 (API返回: None)
2025-07-30 15:00:57,347 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:00:57,347 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:00:57,348 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:00:57,348 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:00:58,349 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:00:58,349 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:00:58,349 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:00:58,352 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_150058.log
2025-07-30 15:00:58,353 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:00:58,353 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:00:58,353 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:00:58,356 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 15:00:58,357 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:00:58,358 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:00:58,362 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 15:00:58,364 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:00:58,365 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 3 个
2025-07-30 15:00:58,371 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:00:58,371 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 15:00:58,372 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 15:00:58,372 - modules.wechat_auto_add_simple - INFO -   4. 微信 (726x650) - main
2025-07-30 15:00:58,372 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:00:58,376 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:00:58,376 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2559444
2025-07-30 15:00:58,377 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2559444) - 增强版
2025-07-30 15:00:58,686 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:00:58,686 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:00:58,687 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:00:58,687 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:00:58,687 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:00:58,688 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:00:58,688 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:00:58,688 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:00:58,891 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:00:58,892 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:00:58,895 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2559444 (API返回: None)
2025-07-30 15:00:59,196 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:00:59,197 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:00:59,197 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:00:59,197 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:00:59,198 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:00:59,198 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:00:59,199 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:00:59,203 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:00:59,203 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:00:59,693 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:00:59,693 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:00:59,941 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2840 个
2025-07-30 15:00:59,942 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2840 个 (总计: 3135 个)
2025-07-30 15:00:59,942 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:00:59,942 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:00:59,943 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:00:59,943 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:00:59,945 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 15:00:59,946 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:00:59,946 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2840
2025-07-30 15:00:59,947 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18681482191 (朱文茜)
2025-07-30 15:00:59,948 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:00:59,948 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:00:59,951 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 15:01:06,520 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18681482191
2025-07-30 15:01:06,521 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:01:06,521 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18681482191 执行添加朋友操作...
2025-07-30 15:01:06,521 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:01:06,522 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:01:06,522 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:01:06,523 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:01:06,529 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:01:06,529 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:01:06,531 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:01:06,531 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:01:06,531 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:01:06,532 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:01:06,532 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:01:06,533 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:01:06,537 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:01:06,538 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:01:06,540 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:01:06,546 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:01:06,550 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:01:06,552 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 15:01:06,553 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:01:07,054 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:01:07,055 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:01:07,147 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.09, 边缘比例0.0378
2025-07-30 15:01:07,159 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_150107.png
2025-07-30 15:01:07,162 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:01:07,164 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:01:07,167 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:01:07,170 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:01:07,172 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:01:07,178 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_150107.png
2025-07-30 15:01:07,181 - WeChatAutoAdd - INFO - 底部区域原始检测到 3 个轮廓
2025-07-30 15:01:07,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,452), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 15:01:07,186 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:01:07,188 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:01:07,189 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:01:07,192 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:01:07,194 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:01:07,199 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:01:07,203 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:01:07,213 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_150107.png
2025-07-30 15:01:07,216 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:01:07,218 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:01:07,223 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_150107.png
2025-07-30 15:01:07,289 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:01:07,291 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:01:07,292 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:01:07,292 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:01:07,594 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:01:08,362 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:01:08,363 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:01:08,365 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:01:08,366 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:01:08,369 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 15:01:08,371 - modules.wechat_auto_add_simple - INFO - ✅ 18681482191 添加朋友操作执行成功
2025-07-30 15:01:08,371 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:01:08,372 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:01:08,374 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 15:01:08,375 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:01:10,415 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:01:10,466 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:01:10,520 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:01:10,571 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:01:10,637 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:01:10,671 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:01:10,716 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:01:10,769 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:01:10,797 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:01:10,891 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18681482191
2025-07-30 15:01:10,924 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:01:10,958 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:01:11,050 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:01:11,110 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:01:11,164 - modules.friend_request_window - INFO -    📱 phone: '18681482191'
2025-07-30 15:01:11,243 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:01:11,363 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:01:13,738 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:01:13,846 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:01:13,899 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:01:13,981 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:01:14,026 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18681482191
2025-07-30 15:01:14,090 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:01:14,158 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:01:14,237 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:01:14,313 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:01:14,360 - modules.friend_request_window - INFO -    📱 手机号码: 18681482191
2025-07-30 15:01:14,423 - modules.friend_request_window - INFO -    🆔 准考证: 014325120008
2025-07-30 15:01:14,502 - modules.friend_request_window - INFO -    👤 姓名: 朱文茜
2025-07-30 15:01:14,560 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:01:14,614 - modules.friend_request_window - INFO -    📝 备注格式: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:14,681 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:01:14,722 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:14,760 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:01:14,787 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1967876, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:01:14,823 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1967876)
2025-07-30 15:01:14,846 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:01:14,862 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:01:14,878 - modules.friend_request_window - INFO - 🔄 激活窗口: 1967876
2025-07-30 15:01:15,657 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:01:15,675 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:01:15,676 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:01:15,728 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:01:15,776 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:01:15,821 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:01:15,859 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:01:15,898 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:01:15,942 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:01:15,971 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:01:16,016 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:01:16,063 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:01:16,138 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:01:16,263 - modules.friend_request_window - INFO -    📝 remark参数: '014325120008-朱文茜-2025-07-30 23:01:14' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:01:16,376 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:01:16,470 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:16,583 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:01:16,705 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:01:16,891 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:01:16,938 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:01:17,017 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:01:17,067 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:01:17,154 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:01:18,373 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:01:23,746 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:01:23,747 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:01:23,747 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:01:23,747 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:01:23,748 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 14:58:20,214 - modules.friend_request_w...' (前50字符)
2025-07-30 15:01:24,061 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:01:24,061 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:01:24,963 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:01:24,974 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:01:24,974 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:01:24,974 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:01:24,976 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:01:24,976 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:01:25,477 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:01:25,477 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:01:25,478 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:01:25,478 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:01:25,478 - modules.friend_request_window - INFO -    📝 内容: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:25,479 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:01:25,479 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120008-\xe6\x9c\xb1\xe6\x96\x87\xe8\x8c\x9c-2025-07-30 23:01:14'
2025-07-30 15:01:25,479 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:01:26,393 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:01:31,640 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:01:31,641 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:01:31,641 - modules.friend_request_window - INFO -    📝 原始文本: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:31,641 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:01:31,642 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '2025-07-30 14:58:20,214 - modules.friend_request_w...' (前50字符)
2025-07-30 15:01:31,951 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:01:31,952 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:01:32,855 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:01:32,866 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:01:32,866 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:32,867 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:01:32,867 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:32,868 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:01:33,369 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120008-朱文茜-2025-07-30 23:01:14'
2025-07-30 15:01:33,370 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:01:33,370 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:01:33,370 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:01:33,370 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:01:33,371 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:01:33,371 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:01:34,172 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:01:34,172 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:01:34,173 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:01:34,792 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:01:34,793 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:01:34,795 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 15:01:34,795 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:01:34,795 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:01:34,796 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:01:35,298 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:01:35,301 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:01:35,301 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:01:35,302 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:01:35,302 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:01:35,302 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:01:35,302 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:01:35,303 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:01:35,303 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:01:35,303 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:01:35,303 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:01:35,304 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:01:35,305 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:01:35,305 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:01:35,306 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:01:35,307 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:01:35,307 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:01:35,310 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 4
2025-07-30 15:01:35,312 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 15:01:35,312 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 8522956)
2025-07-30 15:01:35,313 - modules.friend_request_window - INFO - 🔍 验证窗口 8522956 是否已正确关闭...
2025-07-30 15:01:36,314 - modules.friend_request_window - WARNING - ⚠️ 窗口 8522956 仍然存在且可见，未正确关闭
2025-07-30 15:01:36,314 - modules.friend_request_window - WARNING - ⚠️ 窗口未能正确关闭，暂不添加到黑名单
2025-07-30 15:01:36,314 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 15:01:36,314 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 15:01:36,817 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 15:01:36,818 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 15:01:36,818 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 15:01:36,818 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 15:01:36,818 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 15:01:36,819 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 15:01:36,819 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 15:01:36,819 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 15:01:37,726 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 15:01:37,727 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 15:01:37,727 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 15:01:37,727 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 15:01:37,746 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 15:01:37,746 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 15:01:38,547 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 15:01:38,547 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 15:01:38,548 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 15:01:38,548 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 15:01:38,548 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 15:01:38,549 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 15:01:38,549 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 15:01:38,549 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 15:01:40,050 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 15:01:40,051 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 15:01:40,051 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 15:01:40,051 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 15:01:42,052 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 15:01:42,053 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:01:42,054 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 15:01:42,055 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:01:42,057 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:01:42,057 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:01:42,058 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:01:42,059 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:01:42,061 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:01:42,061 - modules.friend_request_window - INFO - 🔄 使用window_manager切换到下一个微信窗口...
2025-07-30 15:01:42,062 - modules.window_manager - INFO - 📍 当前窗口: 微信 (句柄: 6032430)
2025-07-30 15:01:42,062 - modules.window_manager - INFO - 🔄 切换到微信窗口 2/2: 微信
2025-07-30 15:01:42,062 - modules.window_manager - INFO - 🎯 目标窗口详情: 句柄=6621532, 类名=Qt51514QWindowIcon
2025-07-30 15:01:42,063 - modules.window_manager - INFO - 🚀 开始智能窗口切换: 微信 (句柄: 6621532)
2025-07-30 15:01:42,065 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 15:01:42,671 - modules.window_manager - INFO - ✅ 增强激活策略成功
2025-07-30 15:01:42,972 - modules.window_manager - INFO - ✅ 窗口已成功切换到前台
2025-07-30 15:01:42,972 - modules.window_manager - INFO - ✅ 窗口切换验证成功
2025-07-30 15:01:42,972 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:01:42,973 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:01:42,973 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:01:42,973 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:01:42,974 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:01:42,974 - modules.window_manager - INFO - 📅 安排延迟新闻提示框检测: 微信 (窗口 2)
2025-07-30 15:01:42,975 - modules.window_manager - INFO - ✅ 窗口切换完成: 微信
2025-07-30 15:01:42,975 - modules.window_manager - INFO - ✅ 窗口切换成功，已验证窗口状态
2025-07-30 15:01:42,975 - modules.friend_request_window - INFO - ✅ 成功切换到微信窗口: 微信
2025-07-30 15:01:42,976 - modules.friend_request_window - INFO -    窗口句柄: 6621532
2025-07-30 15:01:42,976 - modules.friend_request_window - INFO -    窗口类名: Qt51514QWindowIcon
2025-07-30 15:01:42,976 - modules.friend_request_window - INFO - 🔧 窗口切换完成，检查添加朋友窗口位置同步...
2025-07-30 15:01:43,986 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 15:01:43,986 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 15:01:44,019 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 15:01:44,521 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 15:01:44,990 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 15:01:44,990 - modules.friend_request_window - WARNING - ⚠️ 未找到添加朋友窗口，尝试等待窗口出现...
2025-07-30 15:01:46,523 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 15:01:46,526 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 15:01:46,993 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 15:01:46,993 - modules.friend_request_window - ERROR - ❌ 仍未找到添加朋友窗口，位置同步失败
2025-07-30 15:01:46,993 - modules.friend_request_window - WARNING - ⚠️ 窗口切换后添加朋友窗口位置同步失败，但继续执行
2025-07-30 15:01:46,994 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 15:01:46,994 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 15:01:46,994 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 15:01:47,028 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 15:01:48,995 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 15:01:48,996 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:01:48,997 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:01:48,997 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 15:01:48,997 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 15:01:48,997 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 15:01:48,998 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:01:48,998 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:01:48,999 - modules.main_interface - WARNING - ⚠️ 当前前台窗口不是微信窗口: 'main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code' (类名: Chrome_WidgetWin_1)
2025-07-30 15:01:48,999 - modules.main_interface - INFO - 🔄 尝试查找并激活微信窗口...
2025-07-30 15:01:49,000 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:01:49,000 - modules.main_interface - INFO - 🔄 创建新的window_manager查找并激活微信窗口...
2025-07-30 15:01:49,001 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:01:49,003 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:01:49,003 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:01:49,006 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:01:49,007 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:01:49,010 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:01:49,011 - modules.main_interface - INFO - 🎯 找到微信窗口: 微信 (类名: Qt51514QWindowIcon, 句柄: 6032430)
2025-07-30 15:01:49,011 - modules.main_interface - INFO - 🚀 使用window_manager激活窗口（包含位置调整）...
2025-07-30 15:01:49,011 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:01:49,436 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 15:01:49,436 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:01:49,437 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:01:49,437 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:01:49,438 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:01:49,438 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:01:49,439 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:01:49,439 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:01:49,439 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:01:49,440 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:01:49,643 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 15:01:49,643 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:01:49,643 - modules.main_interface - INFO - ✅ 微信窗口激活和位置调整成功
2025-07-30 15:01:50,035 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 15:01:50,037 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 15:01:50,538 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 15:01:50,644 - modules.main_interface - INFO - ✅ 微信窗口已激活
2025-07-30 15:01:50,645 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:01:50,645 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:01:50,846 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:01:50,846 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:01:50,847 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:01:53,226 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:01:53,227 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:01:53,227 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 15:01:56,067 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:01:56,268 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:01:56,269 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:01:56,269 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:01:58,656 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:01:58,657 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:01:58,658 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 15:02:00,820 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:02:01,021 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:02:01,021 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:02:01,022 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:02:03,456 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:02:03,536 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:02:03,600 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
