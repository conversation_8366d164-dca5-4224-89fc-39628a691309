2025-07-30 15:23:41,225 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:23:41,226 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:23:41,229 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:23:41,230 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:23:41,232 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:23:41,232 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:23:41,235 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:23:41,238 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 15:23:41,239 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:23:41,244 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:23:41,245 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:23:41,246 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:23:41,246 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:23:41,251 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:23:41,254 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_152341.log
2025-07-30 15:23:41,258 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:23:41,264 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:23:41,265 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:23:41,265 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 15:23:41,266 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 15:23:41,267 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 15:23:41,267 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 23:23:41
2025-07-30 15:23:41,267 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 15:23:41,269 - __main__ - INFO - 📅 启动时间: 2025-07-30 23:23:41
2025-07-30 15:23:41,271 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:23:41,274 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:23:41,821 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:23:41,834 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:23:42,378 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:23:42,379 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:23:42,380 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:23:42,381 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:23:42,382 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 15:23:42,382 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 15:23:42,383 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:23:42,383 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:23:42,384 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:23:42,385 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:23:42,386 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 6032430)
2025-07-30 15:23:42,387 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 6621532)
2025-07-30 15:23:42,388 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 15:23:42,390 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 15:23:42,392 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 6032430)
2025-07-30 15:23:42,394 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 15:23:42,700 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 6621532)
2025-07-30 15:23:42,705 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-30 15:23:42,717 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 15:23:42,718 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 15:23:42,719 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 15:23:42,722 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 15:23:42,735 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 15:23:42,739 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 15:23:44,062 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 15:23:44,062 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 15:23:44,063 - __main__ - INFO - 📋 待处理联系人数: 2840
2025-07-30 15:23:44,063 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 15:23:44,064 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2840
2025-07-30 15:23:44,064 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 2
2025-07-30 15:23:44,065 - __main__ - INFO - 
============================================================
2025-07-30 15:23:44,065 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 15:23:44,066 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:23:44,066 - __main__ - INFO - 📊 全局进度：已处理 0/2840 个联系人（剩余 2840 个）
2025-07-30 15:23:44,067 - __main__ - INFO - ============================================================
2025-07-30 15:23:44,067 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:23:44,068 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 15:23:44,068 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:23:44,069 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:23:44,069 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 6032430)
2025-07-30 15:23:44,070 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-07-30 15:23:44,070 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 15:23:45,071 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:23:46,174 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:23:46,175 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6032430
2025-07-30 15:23:46,176 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:23:46,481 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:23:46,481 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:23:46,481 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:23:46,482 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:23:46,482 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:23:46,482 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:23:46,483 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:23:46,483 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:23:46,483 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:23:46,483 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:23:46,685 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:23:46,686 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:23:46,688 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6032430 (API返回: None)
2025-07-30 15:23:46,989 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:23:46,989 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:23:46,990 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:23:47,991 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:23:47,991 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:23:47,992 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:23:47,992 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:23:47,993 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:23:47,993 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:23:47,993 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:23:47,994 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:23:47,994 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:23:48,196 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:23:48,197 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:23:48,198 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:23:50,605 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:23:50,608 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:23:50,608 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 15:23:52,553 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:23:52,754 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:23:52,754 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:23:52,755 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:23:55,126 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:23:55,126 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:23:55,127 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 15:23:57,828 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:23:58,029 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:23:58,030 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:23:58,030 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:24:00,409 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:24:00,410 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:24:00,410 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 15:24:03,156 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:24:03,357 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:24:03,358 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:24:03,358 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:24:05,743 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:24:05,744 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:24:05,744 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 15:24:07,318 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:24:07,519 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:24:07,520 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:24:07,520 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:24:09,978 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:24:09,978 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:24:09,979 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:24:09,979 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:24:09,979 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:24:09,981 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:24:09,981 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:24:09,982 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 9178316, 进程: Weixin.exe)
2025-07-30 15:24:09,983 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:24:09,983 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:24:09,986 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:24:09,986 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 9178316)
2025-07-30 15:24:09,987 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9178316) - 增强版
2025-07-30 15:24:10,292 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:24:10,293 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:24:10,295 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:24:10,295 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:24:10,296 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:24:10,296 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:24:10,501 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:24:10,502 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:24:10,704 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:24:10,704 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:24:10,705 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:24:10,705 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:24:10,705 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:24:10,705 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:24:10,706 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:24:11,707 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:24:11,707 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:24:11,709 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:24:11,709 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 15:24:11,711 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 9178316, 进程: Weixin.exe)
2025-07-30 15:24:11,711 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:24:11,712 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:24:11,714 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:24:11,715 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:24:11,715 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:24:11,715 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:24:11,716 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:24:11,716 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6032430
2025-07-30 15:24:11,716 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:24:12,025 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:24:12,026 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:24:12,027 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:24:12,027 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:24:12,028 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:24:12,028 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:24:12,028 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:24:12,029 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:24:12,029 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:24:12,029 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:24:12,231 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:24:12,232 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:24:12,233 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6032430 (API返回: None)
2025-07-30 15:24:12,534 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:24:12,534 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:24:12,535 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:24:12,535 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:24:13,535 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:24:13,536 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:24:13,536 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:24:13,541 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_152413.log
2025-07-30 15:24:13,543 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:24:13,543 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:24:13,544 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:24:13,544 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 15:24:13,544 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:24:13,545 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:24:13,548 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 15:24:13,549 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:24:13,550 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 15:24:13,550 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:24:13,551 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 15:24:13,551 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 15:24:13,551 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:24:13,552 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:24:13,553 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 9178316
2025-07-30 15:24:13,553 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9178316) - 增强版
2025-07-30 15:24:13,866 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:24:13,866 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:24:13,867 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:24:13,867 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:24:13,867 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:24:13,868 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:24:13,868 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:24:13,868 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:24:14,070 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:24:14,070 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:24:14,073 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 9178316 (API返回: None)
2025-07-30 15:24:14,374 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:24:14,375 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:24:14,375 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:24:14,376 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:24:14,377 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:24:14,377 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:24:14,378 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:24:14,394 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:24:14,395 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:24:14,970 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:24:14,972 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:24:15,296 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2840 个
2025-07-30 15:24:15,296 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2840 个 (总计: 3135 个)
2025-07-30 15:24:15,297 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:24:15,297 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:24:15,298 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:24:15,298 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:24:15,300 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:24:15,300 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:24:15,301 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2840
2025-07-30 15:24:15,301 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18681482191 (朱文茜)
2025-07-30 15:24:15,302 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:24:15,302 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:24:15,305 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:24:21,880 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18681482191
2025-07-30 15:24:21,880 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:24:21,881 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18681482191 执行添加朋友操作...
2025-07-30 15:24:21,881 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:24:21,881 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:24:21,882 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:24:21,883 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:24:21,889 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:24:21,891 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:24:21,891 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:24:21,892 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:24:21,892 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:24:21,893 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:24:21,893 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:24:21,894 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:24:21,899 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:24:21,900 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:24:21,908 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:24:21,914 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:24:21,919 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 15:24:21,922 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:24:22,425 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:24:22,426 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:24:22,520 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.43, 边缘比例0.0378
2025-07-30 15:24:22,528 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_152422.png
2025-07-30 15:24:22,529 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:24:22,530 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:24:22,531 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:24:22,532 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:24:22,532 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:24:22,537 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_152422.png
2025-07-30 15:24:22,540 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:24:22,541 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:24:22,543 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:24:22,544 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:24:22,545 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:24:22,546 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:24:22,547 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:24:22,553 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:24:22,564 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_152422.png
2025-07-30 15:24:22,567 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:24:22,568 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:24:22,572 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_152422.png
2025-07-30 15:24:22,645 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:24:22,647 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:24:22,648 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:24:22,649 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:24:22,960 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:24:23,773 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:24:23,774 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:24:23,776 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:24:23,776 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:24:23,778 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:24:23,778 - modules.wechat_auto_add_simple - INFO - ✅ 18681482191 添加朋友操作执行成功
2025-07-30 15:24:23,779 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:24:23,779 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:24:23,782 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:24:23,782 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:24:25,784 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:24:25,785 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:24:25,786 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:24:25,786 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:24:25,788 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:24:25,789 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:24:25,789 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:24:25,790 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:24:25,797 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:24:25,800 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18681482191
2025-07-30 15:24:25,809 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:24:25,814 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:24:25,815 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:24:25,816 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:24:25,817 - modules.friend_request_window - INFO -    📱 phone: '18681482191'
2025-07-30 15:24:25,817 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:24:25,818 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:24:26,411 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:24:26,411 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:24:26,412 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:24:26,412 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:24:26,414 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18681482191
2025-07-30 15:24:26,414 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:24:26,415 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:24:26,417 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:24:26,418 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:24:26,418 - modules.friend_request_window - INFO -    📱 手机号码: 18681482191
2025-07-30 15:24:26,419 - modules.friend_request_window - INFO -    🆔 准考证: 014325120008
2025-07-30 15:24:26,419 - modules.friend_request_window - INFO -    👤 姓名: 朱文茜
2025-07-30 15:24:26,419 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:24:26,421 - modules.friend_request_window - INFO -    📝 备注格式: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:26,422 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:24:26,424 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:26,425 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:24:26,438 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1314300, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:24:26,452 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1314300)
2025-07-30 15:24:26,461 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:24:26,462 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:24:26,463 - modules.friend_request_window - INFO - 🔄 激活窗口: 1314300
2025-07-30 15:24:27,167 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:24:27,167 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:24:27,168 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:24:27,168 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:24:27,168 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:24:27,169 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:24:27,169 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:24:27,169 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:24:27,169 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:24:27,171 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:24:27,173 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:24:27,174 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:24:27,175 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:24:27,176 - modules.friend_request_window - INFO -    📝 remark参数: '014325120008-朱文茜-2025-07-30 23:24:26' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:24:27,177 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:24:27,177 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:27,178 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:24:27,178 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:24:27,178 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:24:27,178 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:24:27,179 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:24:27,179 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:24:27,179 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:24:28,090 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:24:33,339 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:24:33,339 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:24:33,339 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:24:33,340 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:24:33,342 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标(700, 16)强制关闭微信窗口...' (前50字符)
2025-07-30 15:24:33,654 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:24:33,655 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:24:34,557 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:24:34,567 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:24:34,573 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:24:34,574 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:24:34,575 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:24:34,575 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:24:35,076 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:24:35,077 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:24:35,077 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:24:35,077 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:24:35,077 - modules.friend_request_window - INFO -    📝 内容: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:35,078 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:24:35,078 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120008-\xe6\x9c\xb1\xe6\x96\x87\xe8\x8c\x9c-2025-07-30 23:24:26'
2025-07-30 15:24:35,078 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:24:36,032 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:24:41,376 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:24:41,377 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:24:41,377 - modules.friend_request_window - INFO -    📝 原始文本: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:41,377 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:24:41,378 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '坐标(700, 16)强制关闭微信窗口...' (前50字符)
2025-07-30 15:24:41,690 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:24:41,691 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:24:42,594 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:24:42,606 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:24:42,607 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:42,608 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:24:42,610 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:42,611 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:24:43,112 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120008-朱文茜-2025-07-30 23:24:26'
2025-07-30 15:24:43,113 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:24:43,113 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:24:43,113 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:24:43,114 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:24:43,114 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:24:43,114 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:24:43,915 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:24:43,915 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:24:43,916 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:24:44,564 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:24:44,565 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:24:44,567 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:24:44,567 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:24:44,568 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:24:44,568 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:24:45,070 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:24:45,075 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:24:45,075 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:24:45,075 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:24:45,076 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:24:45,076 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:24:45,078 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:24:45,079 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:24:45,080 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:24:45,080 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:24:45,084 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:24:45,085 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:24:45,087 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:24:45,089 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:24:45,091 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:24:45,093 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:24:45,093 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:24:45,098 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 3
2025-07-30 15:24:45,099 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 15:24:45,100 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 7014100)
2025-07-30 15:24:45,104 - modules.friend_request_window - INFO - ✅ 频率错误窗口已强制添加到黑名单
2025-07-30 15:24:45,105 - modules.friend_request_window - INFO - 📊 当前黑名单窗口数量: 1
2025-07-30 15:24:45,108 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 15:24:45,108 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 15:24:45,610 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 15:24:45,610 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 15:24:45,611 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 15:24:45,611 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 15:24:45,611 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 15:24:45,612 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 15:24:45,612 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 15:24:45,612 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 15:24:46,524 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 15:24:46,525 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 15:24:46,525 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 15:24:46,525 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 15:24:46,546 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 15:24:46,546 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 15:24:47,347 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 15:24:47,347 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 15:24:47,347 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 15:24:47,348 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 15:24:47,348 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 15:24:47,348 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 15:24:47,349 - modules.friend_request_window - INFO - 🎯 强制使用坐标(700, 16)关闭微信主窗口
2025-07-30 15:24:47,349 - modules.friend_request_window - INFO -    激活窗口...
2025-07-30 15:24:47,849 - modules.friend_request_window - INFO -    使用坐标(700, 16)点击关闭按钮...
2025-07-30 15:24:49,475 - modules.friend_request_window - INFO - ✅ 第一次坐标点击成功关闭微信主窗口
2025-07-30 15:24:49,475 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 15:24:49,475 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 15:24:49,476 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 15:24:51,477 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 15:24:51,478 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:24:51,478 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 15:24:51,478 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:24:51,480 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:24:51,481 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 15:24:51,483 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:24:51,483 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 15:24:51,483 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 15:24:51,509 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 15:24:51,509 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 15:24:51,510 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 15:24:51,511 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 6032430
2025-07-30 15:24:51,512 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:24:51,512 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 15:24:51,513 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 15:24:52,036 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:24:52,036 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:24:52,037 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:24:52,037 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:24:52,037 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:24:52,038 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:24:52,038 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:24:52,038 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:24:52,038 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:24:52,039 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:24:52,241 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:24:52,241 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:24:52,241 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 15:24:52,242 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 15:24:52,242 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 15:24:52,242 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 15:24:52,243 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 15:24:54,244 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 15:24:54,246 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:24:54,247 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:24:54,247 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 15:24:54,247 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 15:24:54,248 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 15:24:54,248 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:24:54,248 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:24:54,249 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:24:54,249 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:24:54,251 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:24:54,252 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:24:54,454 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:24:54,455 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:24:54,455 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:24:56,917 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:24:56,918 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:24:56,921 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 15:24:58,999 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:24:59,201 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:24:59,202 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:24:59,203 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:25:01,595 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:25:01,595 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:25:01,596 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 15:25:04,311 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:25:04,511 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:25:04,512 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:25:04,512 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:25:06,991 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:25:07,045 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:25:07,046 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 15:25:08,743 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:25:08,979 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:25:08,980 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:25:08,981 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
