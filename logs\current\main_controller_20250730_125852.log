2025-07-30 12:58:52,240 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:58:52,241 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 12:58:52,243 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:58:52,244 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:58:52,245 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:58:52,246 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:58:52,249 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:58:52,251 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:58:52,251 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:58:52,252 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:58:52,252 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:58:52,253 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:58:52,255 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:58:52,270 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_125852.log
2025-07-30 12:58:52,341 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:58:52,379 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:58:52,414 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:58:52,446 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:58:52,495 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:58:52,498 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:58:52,499 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 20:58:52
2025-07-30 12:58:52,502 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:58:52,505 - __main__ - INFO - 📅 启动时间: 2025-07-30 20:58:52
2025-07-30 12:58:52,506 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:58:52,507 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:58:53,059 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:58:53,061 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:58:53,590 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:58:53,590 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:58:53,593 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:58:53,594 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 12:58:53,594 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 12:58:53,594 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 12:58:53,595 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 12:58:53,595 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 12:58:53,595 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 12:58:53,596 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 12:58:53,596 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 263568)
2025-07-30 12:58:53,597 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:58:53,597 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 12:58:53,597 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 12:58:53,598 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 263568)
2025-07-30 12:58:53,598 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 12:58:53,899 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 9045874)
2025-07-30 12:58:53,900 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-30 12:58:53,900 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 12:58:53,901 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 12:58:53,901 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 12:58:53,902 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 12:58:53,902 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 12:58:53,903 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 12:58:55,553 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 12:58:55,596 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 12:58:55,649 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:58:55,665 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:58:55,666 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 12:58:55,667 - __main__ - INFO - 
============================================================
2025-07-30 12:58:55,670 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 12:58:55,671 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:58:55,673 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:58:55,673 - __main__ - INFO - ============================================================
2025-07-30 12:58:55,674 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:58:55,676 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:58:55,678 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:58:55,679 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:58:55,703 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 263568)
2025-07-30 12:58:55,754 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-30 12:58:55,765 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 12:58:56,768 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 12:58:56,769 - __main__ - WARNING - ⚠️ SetForegroundWindow 异常: (0, 'SetForegroundWindow', 'No error message is available')
2025-07-30 12:58:57,882 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 12:58:57,882 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:58:57,882 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:58:58,293 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 12:58:58,294 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:58:58,295 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:58:58,295 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:58:58,295 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:58:58,296 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:58:58,296 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:58:58,297 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:58:58,297 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:58:58,297 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:58:58,499 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 12:58:58,499 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:58:58,503 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:58:58,804 - modules.window_manager - WARNING - ⚠️ 窗口可能未完全置于最前面 (当前前台: 2297084)
2025-07-30 12:58:58,804 - __main__ - WARNING - ⚠️ 窗口激活可能不完全，但继续执行 (当前前台: 2297084)
2025-07-30 12:58:58,805 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:58:59,805 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:58:59,806 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 12:58:59,806 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:58:59,806 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:58:59,807 - modules.main_interface - WARNING - ⚠️ 当前前台窗口不是微信窗口: 'main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code' (类名: Chrome_WidgetWin_1)
2025-07-30 12:58:59,807 - modules.main_interface - INFO - 🔄 尝试查找并激活微信窗口...
2025-07-30 12:58:59,807 - modules.main_interface - INFO - 🔄 使用已初始化的window_manager查找并激活微信窗口...
2025-07-30 12:58:59,808 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:58:59,811 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:58:59,811 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:58:59,812 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:58:59,813 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:58:59,815 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:58:59,816 - modules.main_interface - INFO - 🎯 找到微信窗口: 微信 (类名: Qt51514QWindowIcon, 句柄: 263568)
2025-07-30 12:58:59,816 - modules.main_interface - INFO - 🚀 使用window_manager激活窗口（包含位置调整）...
2025-07-30 12:58:59,816 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:59:00,227 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 12:59:00,227 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:59:00,228 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:59:00,228 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:59:00,228 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:59:00,228 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:59:00,229 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:59:00,229 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:59:00,229 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:59:00,230 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:59:00,433 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 12:59:00,433 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:59:00,433 - modules.main_interface - INFO - ✅ 微信窗口激活和位置调整成功
2025-07-30 12:59:01,437 - modules.main_interface - INFO - ✅ 微信窗口已激活
2025-07-30 12:59:01,450 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:59:01,456 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:59:01,716 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:59:01,717 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:59:04,097 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:59:04,097 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:59:04,097 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-30 12:59:07,060 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:59:07,261 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:59:07,261 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:59:09,645 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:59:09,645 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:59:09,646 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 12:59:12,527 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:59:12,728 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:59:12,728 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:59:15,111 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:59:15,112 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:59:15,112 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 12:59:17,457 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:59:17,658 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:59:17,659 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:59:20,028 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:59:20,028 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:59:20,028 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 12:59:22,171 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:59:22,372 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:59:22,372 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:59:24,744 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:59:24,745 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:59:24,745 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:59:24,746 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:59:24,747 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:59:24,749 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:59:24,749 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:59:24,751 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6163780, 进程: Weixin.exe)
2025-07-30 12:59:24,753 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:59:24,754 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:59:24,757 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:59:24,758 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 6163780)
2025-07-30 12:59:24,764 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6163780) - 增强版
2025-07-30 12:59:25,067 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:59:25,067 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:59:25,068 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:59:25,068 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:59:25,068 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 12:59:25,069 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:59:25,273 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 12:59:25,273 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:59:25,474 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:59:25,475 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:59:25,475 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 12:59:25,475 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 12:59:25,476 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 12:59:25,476 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:59:25,476 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 12:59:26,477 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 12:59:26,477 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:59:26,479 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:59:26,479 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:59:26,480 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 6163780, 进程: Weixin.exe)
2025-07-30 12:59:26,481 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:59:26,481 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:59:26,483 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:59:26,484 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 12:59:26,484 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 12:59:26,484 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 12:59:26,485 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:59:26,485 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:59:26,800 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:59:26,800 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:59:26,801 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:59:26,801 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:59:26,802 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:59:26,803 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:59:26,803 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:59:26,804 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:59:26,804 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:59:26,805 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:59:27,007 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:59:27,008 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:59:27,012 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:59:27,313 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:59:27,313 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 12:59:27,313 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 12:59:28,314 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 12:59:28,314 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 12:59:28,315 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 12:59:28,317 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_125928.log
2025-07-30 12:59:28,318 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:59:28,319 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:59:28,319 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:59:28,319 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 12:59:28,320 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 12:59:28,320 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 12:59:28,322 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 12:59:28,324 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 12:59:28,325 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 12:59:28,326 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 12:59:28,328 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 12:59:28,329 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 12:59:28,330 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 12:59:28,332 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:59:28,333 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6163780
2025-07-30 12:59:28,337 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6163780) - 增强版
2025-07-30 12:59:28,659 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:59:28,660 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:59:28,660 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:59:28,661 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:59:28,661 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 12:59:28,662 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:59:28,662 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 12:59:28,662 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:59:28,865 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:59:28,865 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:59:28,869 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6163780 (API返回: None)
2025-07-30 12:59:29,170 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:59:29,170 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 12:59:29,171 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 12:59:29,171 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 12:59:29,172 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:59:29,172 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 12:59:29,173 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 12:59:29,177 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 12:59:29,177 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 12:59:29,687 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 12:59:29,687 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:59:29,985 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2899 个
2025-07-30 12:59:29,986 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2899 个 (总计: 3135 个)
2025-07-30 12:59:29,986 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 12:59:29,987 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 12:59:29,987 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:59:29,988 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 12:59:29,988 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 12:59:29,988 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2899
2025-07-30 12:59:29,989 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15968490212 (邹铠骏)
2025-07-30 12:59:29,990 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:59:29,990 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 12:59:36,570 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15968490212
2025-07-30 12:59:36,570 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 12:59:36,571 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15968490212 执行添加朋友操作...
2025-07-30 12:59:36,571 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 12:59:36,571 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 12:59:36,572 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:59:36,573 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 12:59:36,575 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 2 个图片文件
2025-07-30 12:59:36,576 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 12:59:36,578 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 12:59:36,578 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 12:59:36,578 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 12:59:36,579 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 12:59:36,579 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 12:59:36,580 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 12:59:36,584 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 12:59:36,585 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:59:36,587 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:59:36,596 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 12:59:36,601 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 12:59:36,617 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 12:59:37,120 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 12:59:37,121 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 12:59:37,205 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 12:59:37,206 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 12:59:37,214 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_125937.png
2025-07-30 12:59:37,215 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 12:59:37,218 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 12:59:37,220 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 12:59:37,224 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 12:59:37,227 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 12:59:37,236 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_125937.png
2025-07-30 12:59:37,237 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 12:59:37,238 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 12:59:37,241 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 12:59:37,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 12:59:37,247 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 12:59:37,250 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 12:59:37,251 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 12:59:37,252 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 12:59:37,261 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_125937.png
2025-07-30 12:59:37,266 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 12:59:37,268 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 12:59:37,274 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_125937.png
2025-07-30 12:59:37,341 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 12:59:37,343 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 12:59:37,344 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 12:59:37,345 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 12:59:37,646 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 12:59:38,416 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 12:59:38,416 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 12:59:38,418 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:59:38,418 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 12:59:38,419 - modules.wechat_auto_add_simple - INFO - ✅ 15968490212 添加朋友操作执行成功
2025-07-30 12:59:38,419 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:59:38,420 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 12:59:38,420 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 12:59:40,422 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 12:59:40,422 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 12:59:40,422 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 12:59:40,423 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:59:40,423 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:59:40,423 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:59:40,424 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:59:40,424 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:59:40,424 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 12:59:40,425 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15968490212
2025-07-30 12:59:40,429 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 12:59:40,429 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:59:40,429 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:59:40,430 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 12:59:40,431 - modules.friend_request_window - INFO -    📱 phone: '15968490212'
2025-07-30 12:59:40,431 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 12:59:40,432 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 12:59:40,956 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 12:59:40,957 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 12:59:40,957 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 12:59:40,957 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:59:40,959 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15968490212
2025-07-30 12:59:40,959 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 12:59:40,960 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:59:40,961 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 12:59:40,962 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 12:59:40,962 - modules.friend_request_window - INFO -    📱 手机号码: 15968490212
2025-07-30 12:59:40,962 - modules.friend_request_window - INFO -    🆔 准考证: 014325110153
2025-07-30 12:59:40,963 - modules.friend_request_window - INFO -    👤 姓名: 邹铠骏
2025-07-30 12:59:40,964 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 12:59:40,966 - modules.friend_request_window - INFO -    📝 备注格式: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:40,968 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:59:40,969 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:40,981 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 12:59:40,983 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 9243718, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 12:59:40,985 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 9243718)
2025-07-30 12:59:40,985 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 12:59:40,986 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 12:59:40,986 - modules.friend_request_window - INFO - 🔄 激活窗口: 9243718
2025-07-30 12:59:41,689 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 12:59:41,691 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 12:59:41,692 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 12:59:41,692 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 12:59:41,692 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 12:59:41,692 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:59:41,693 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 12:59:41,693 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:59:41,693 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 12:59:41,694 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 12:59:41,694 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 12:59:41,695 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 12:59:41,695 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 12:59:41,696 - modules.friend_request_window - INFO -    📝 remark参数: '014325110153-邹铠骏-2025-07-30 20:59:40' (类型: <class 'str'>, 长度: 36)
2025-07-30 12:59:41,697 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 12:59:41,698 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:41,699 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 12:59:41,699 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 12:59:41,699 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 12:59:41,700 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 12:59:41,700 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 12:59:41,700 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 12:59:41,700 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 12:59:42,609 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 12:59:47,853 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 12:59:47,854 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 12:59:47,854 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 12:59:47,854 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 12:59:47,855 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '理方式...

2025-07-30 12:45:03,808 - modules.friend_r...' (前50字符)
2025-07-30 12:59:48,168 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:59:48,168 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:59:49,071 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:59:49,082 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:59:49,082 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 12:59:49,083 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 12:59:49,083 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 12:59:49,083 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 12:59:49,584 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 12:59:49,585 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 12:59:49,585 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 12:59:49,585 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 12:59:49,585 - modules.friend_request_window - INFO -    📝 内容: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:49,586 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 12:59:49,586 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110153-\xe9\x82\xb9\xe9\x93\xa0\xe9\xaa\x8f-2025-07-30 20:59:40'
2025-07-30 12:59:49,586 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 12:59:50,492 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 12:59:55,738 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 12:59:55,738 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 12:59:55,738 - modules.friend_request_window - INFO -    📝 原始文本: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:55,739 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 12:59:55,739 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '理方式...

2025-07-30 12:45:03,808 - modules.friend_r...' (前50字符)
2025-07-30 12:59:56,049 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:59:56,050 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:59:56,952 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:59:56,962 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:59:56,963 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:56,963 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 12:59:56,964 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:56,964 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 12:59:57,465 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 12:59:57,465 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 12:59:57,466 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 12:59:57,466 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 12:59:57,466 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 12:59:57,467 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 12:59:57,467 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 12:59:58,268 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 12:59:58,269 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 12:59:58,269 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 12:59:58,876 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:59:58,877 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 12:59:58,877 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 12:59:58,877 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 12:59:58,877 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 12:59:59,395 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 12:59:59,632 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 12:59:59,868 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:00,106 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:00,340 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:00,575 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:00,816 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:01,053 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:01,289 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:01,525 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:01,760 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:01,996 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:02,233 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:02,468 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:02,703 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:02,940 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:03,176 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:03,417 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:03,652 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:03,885 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:00:04,100 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 13:00:04,101 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 13:00:05,101 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:00:05,106 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 13:00:05,107 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 13:00:05,107 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 13:00:05,108 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 13:00:05,108 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:00:05,109 - modules.friend_request_window - INFO -    📝 备注信息: '014325110153-邹铠骏-2025-07-30 20:59:40'
2025-07-30 13:00:05,610 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 13:00:05,611 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:00:05,611 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 13:00:05,612 - modules.wechat_auto_add_simple - INFO - ✅ 15968490212 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 13:00:05,612 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15968490212
2025-07-30 13:00:05,613 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:00:05,614 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 13:00:09,225 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2899
2025-07-30 13:00:09,226 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15217214768 (李莹莹)
2025-07-30 13:00:09,227 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:00:09,227 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 13:00:15,817 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15217214768
2025-07-30 13:00:15,817 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 13:00:15,817 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15217214768 执行添加朋友操作...
2025-07-30 13:00:15,818 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 13:00:15,818 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 13:00:15,819 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 13:00:15,820 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 13:00:15,824 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 13:00:15,826 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 13:00:15,827 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 13:00:15,828 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 13:00:15,828 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 13:00:15,828 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 13:00:15,829 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 13:00:15,832 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 13:00:15,838 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:00:15,841 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:00:15,843 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:00:15,845 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 13:00:15,850 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 13:00:15,852 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 13:00:16,354 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 13:00:16,356 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 13:00:16,422 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 13:00:16,423 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 13:00:16,431 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_130016.png
2025-07-30 13:00:16,433 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 13:00:16,435 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 13:00:16,436 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 13:00:16,441 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 13:00:16,443 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 13:00:16,447 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_130016.png
2025-07-30 13:00:16,449 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 13:00:16,450 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 13:00:16,451 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 13:00:16,452 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 13:00:16,453 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 13:00:16,458 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 13:00:16,460 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 13:00:16,461 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 13:00:16,469 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_130016.png
2025-07-30 13:00:16,472 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 13:00:16,478 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 13:00:16,483 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_130016.png
2025-07-30 13:00:16,509 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 13:00:16,512 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 13:00:16,515 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 13:00:16,517 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 13:00:16,819 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 13:00:17,587 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 13:00:17,590 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 13:00:17,592 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:00:17,593 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 13:00:17,593 - modules.wechat_auto_add_simple - INFO - ✅ 15217214768 添加朋友操作执行成功
2025-07-30 13:00:17,594 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:00:17,594 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 13:00:17,595 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 13:00:19,596 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 13:00:19,597 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 13:00:19,597 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 13:00:19,597 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 13:00:19,598 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 13:00:19,598 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 13:00:19,598 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 13:00:19,598 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 13:00:19,599 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 13:00:19,599 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15217214768
2025-07-30 13:00:19,600 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 13:00:19,600 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:00:19,600 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:00:19,601 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 13:00:19,601 - modules.friend_request_window - INFO -    📱 phone: '15217214768'
2025-07-30 13:00:19,601 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 13:00:19,601 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 13:00:20,106 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 13:00:20,107 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 13:00:20,107 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 13:00:20,107 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:00:20,109 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15217214768
2025-07-30 13:00:20,109 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 13:00:20,110 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:00:20,110 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 13:00:20,111 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 13:00:20,111 - modules.friend_request_window - INFO -    📱 手机号码: 15217214768
2025-07-30 13:00:20,112 - modules.friend_request_window - INFO -    🆔 准考证: 014325110157
2025-07-30 13:00:20,112 - modules.friend_request_window - INFO -    👤 姓名: 李莹莹
2025-07-30 13:00:20,112 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:00:20,113 - modules.friend_request_window - INFO -    📝 备注格式: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:20,114 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:00:20,114 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:20,114 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:00:20,116 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 4393984, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 13:00:20,119 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 4393984)
2025-07-30 13:00:20,120 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 13:00:20,121 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 13:00:20,121 - modules.friend_request_window - INFO - 🔄 激活窗口: 4393984
2025-07-30 13:00:20,824 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 13:00:20,825 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 13:00:20,825 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 13:00:20,825 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 13:00:20,826 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 13:00:20,826 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:00:20,826 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 13:00:20,826 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:00:20,827 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 13:00:20,827 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 13:00:20,827 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 13:00:20,827 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 13:00:20,828 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 13:00:20,828 - modules.friend_request_window - INFO -    📝 remark参数: '014325110157-李莹莹-2025-07-30 21:00:20' (类型: <class 'str'>, 长度: 36)
2025-07-30 13:00:20,828 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 13:00:20,829 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:20,829 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 13:00:20,829 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 13:00:20,829 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 13:00:20,830 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 13:00:20,830 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 13:00:20,830 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 13:00:20,831 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 13:00:21,740 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 13:00:26,990 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 13:00:26,990 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 13:00:26,990 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 13:00:26,991 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 13:00:26,992 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '理方式...

2025-07-30 12:45:03,808 - modules.friend_r...' (前50字符)
2025-07-30 13:00:27,302 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:00:27,302 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:00:28,205 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:00:28,214 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:00:28,214 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 13:00:28,215 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 13:00:28,215 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 13:00:28,216 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 13:00:28,716 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 13:00:28,717 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 13:00:28,717 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 13:00:28,717 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 13:00:28,718 - modules.friend_request_window - INFO -    📝 内容: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:28,718 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 13:00:28,718 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110157-\xe6\x9d\x8e\xe8\x8e\xb9\xe8\x8e\xb9-2025-07-30 21:00:20'
2025-07-30 13:00:28,719 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 13:00:29,640 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 13:00:34,884 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 13:00:34,884 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 13:00:34,885 - modules.friend_request_window - INFO -    📝 原始文本: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:34,885 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 13:00:34,886 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '理方式...

2025-07-30 12:45:03,808 - modules.friend_r...' (前50字符)
2025-07-30 13:00:35,196 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:00:35,197 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:00:36,099 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:00:36,109 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:00:36,109 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:36,109 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 13:00:36,110 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:36,111 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 13:00:36,611 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:00:36,612 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 13:00:36,613 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 13:00:36,613 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 13:00:36,613 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 13:00:36,613 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 13:00:36,614 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 13:00:37,414 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 13:00:37,415 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 13:00:37,415 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 13:00:38,024 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:00:38,024 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 13:00:38,025 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 13:00:38,025 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 13:00:38,026 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 13:00:38,528 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 13:00:38,531 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 13:00:38,531 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 13:00:38,532 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 13:00:38,532 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 13:00:38,532 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 13:00:38,533 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 13:00:38,533 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 13:00:38,533 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 13:00:38,534 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:00:38,534 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 13:00:38,537 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 13:00:38,538 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 13:00:38,539 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 13:00:38,543 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 13:00:38,544 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 13:00:38,544 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:00:38,545 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 13:00:38,549 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 13:00:38,549 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 13:00:38,549 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 13:00:39,052 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 13:00:39,052 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 13:00:39,053 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 13:00:39,053 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 13:00:39,053 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 13:00:39,053 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 13:00:39,054 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 13:00:39,054 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 13:00:39,973 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 13:00:39,974 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 13:00:39,974 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 13:00:39,975 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 13:00:39,992 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 13:00:39,992 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 13:00:40,793 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 13:00:40,793 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 13:00:40,794 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 13:00:40,794 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 13:00:40,794 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 13:00:40,795 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 13:00:40,795 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 13:00:40,795 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 13:00:42,296 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 13:00:42,297 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 13:00:42,297 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 13:00:42,297 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 13:00:44,298 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 13:00:44,299 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:00:44,299 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 13:00:44,300 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:00:44,302 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:00:44,302 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 13:00:44,304 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 13:00:44,304 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 13:00:44,305 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 13:00:44,324 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 13:00:44,324 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 13:00:44,328 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 13:00:44,328 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 9045874
2025-07-30 13:00:44,330 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:00:44,331 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9045874) - 增强版
2025-07-30 13:00:44,635 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:00:44,635 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:00:44,635 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:00:44,636 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:00:44,636 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:00:44,636 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:00:44,637 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:00:44,637 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:00:44,637 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:00:44,637 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:00:44,839 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:00:44,840 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:00:44,840 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 13:00:44,840 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 13:00:44,841 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 13:00:44,841 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 13:00:44,841 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 13:00:46,842 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 13:00:46,844 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:00:46,844 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 13:00:46,844 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 13:00:46,845 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 13:00:46,845 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 13:00:46,845 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:00:46,846 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:00:46,846 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:00:46,847 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:00:46,848 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:00:46,849 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:00:47,050 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:00:47,051 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:00:49,422 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:00:49,422 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:00:49,423 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 13:00:51,767 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:00:51,968 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:00:51,968 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:00:54,356 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:00:54,356 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:00:54,356 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 13:00:56,108 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 13:00:56,309 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 13:00:56,309 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 13:00:58,679 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 13:00:58,679 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 13:00:58,679 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 13:01:00,792 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 13:01:00,993 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 13:01:00,994 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 13:01:03,362 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 13:01:03,362 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 13:01:03,363 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 13:01:05,535 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 13:01:05,737 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 13:01:05,737 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 13:01:08,122 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 13:01:08,122 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 13:01:08,123 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:01:08,123 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:01:08,124 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:01:08,124 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:01:08,126 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:01:08,126 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 13:01:08,127 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7996154, 进程: Weixin.exe)
2025-07-30 13:01:08,129 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 13:01:08,129 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 7996154)
2025-07-30 13:01:08,129 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 7996154) - 增强版
2025-07-30 13:01:08,433 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:01:08,433 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:01:08,434 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:01:08,434 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:01:08,434 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 13:01:08,435 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:01:08,639 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 13:01:08,639 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:01:08,842 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:01:08,842 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:01:08,842 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 13:01:08,843 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 13:01:08,843 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 13:01:08,844 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 13:01:08,844 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 13:01:09,862 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 13:01:09,922 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:01:09,990 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:01:10,103 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:01:10,221 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 13:01:10,275 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 7996154, 进程: Weixin.exe)
2025-07-30 13:01:10,312 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 13:01:10,378 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 13:01:10,420 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 13:01:10,455 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 13:01:10,506 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 13:01:10,536 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 13:01:10,578 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 13:01:11,612 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 13:01:11,634 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 13:01:11,676 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 13:01:11,715 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 13:01:11,783 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 13:01:11,834 - modules.friend_request_window - INFO - 🔧 开始移动窗口 7996154 到目标位置...
2025-07-30 13:01:12,757 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 13:01:12,822 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 13:01:12,902 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 13:01:12,970 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 13:01:13,032 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 13:01:13,101 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 13:01:13,152 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 13:01:13,204 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 13:01:13,256 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 13:01:13,292 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 13:01:13,361 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 13:01:13,405 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:01:13,439 - modules.friend_request_window - INFO -    📝 备注信息: '014325110157-李莹莹-2025-07-30 21:00:20'
2025-07-30 13:01:13,998 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 13:01:14,006 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:01:14,018 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 13:01:14,034 - modules.wechat_auto_add_simple - INFO - ✅ 15217214768 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 13:01:14,046 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15217214768
2025-07-30 13:01:14,061 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:01:14,072 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 13:01:18,195 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 13:01:18,203 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 13:01:18,219 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 13:01:18,226 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 13:01:18,232 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 13:01:18,241 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 13:01:18,243 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 13:01:18,258 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 13:01:18,258 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 13:01:18,296 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 13:01:18,330 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 13:01:18,338 - __main__ - INFO - � 更新全局进度：已处理 2/2899 个联系人
2025-07-30 13:01:18,340 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 13:01:21,348 - __main__ - INFO - 
============================================================
2025-07-30 13:01:21,349 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 13:01:21,349 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 9045874)
2025-07-30 13:01:21,349 - __main__ - INFO - 📊 全局进度：已处理 2/2899 个联系人
2025-07-30 13:01:21,350 - __main__ - INFO - ============================================================
2025-07-30 13:01:21,350 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 13:01:21,350 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 9045874)
2025-07-30 13:01:21,351 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 13:01:21,351 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 13:01:21,351 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 9045874)
2025-07-30 13:01:21,351 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 13:01:22,454 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 13:01:22,455 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 9045874
2025-07-30 13:01:22,455 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9045874) - 增强版
2025-07-30 13:01:22,758 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:01:22,759 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:01:22,759 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:01:22,760 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:01:22,760 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:01:22,760 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:01:22,761 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:01:22,761 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:01:22,762 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:01:22,762 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:01:22,964 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:01:22,964 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:01:22,967 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 9045874 (API返回: None)
2025-07-30 13:01:23,267 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:01:23,268 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 13:01:23,268 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 13:01:24,269 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 13:01:24,269 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 13:01:24,270 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:01:24,270 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:01:24,270 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:01:24,271 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:01:24,271 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:01:24,271 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:01:24,472 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:01:24,472 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:01:26,853 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:01:26,853 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:01:26,853 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 13:01:28,457 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:01:28,658 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:01:28,659 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:01:31,043 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:01:31,045 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:01:31,046 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 13:01:32,921 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 13:01:33,122 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 13:01:33,122 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
