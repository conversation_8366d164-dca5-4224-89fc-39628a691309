2025-07-30 12:44:01,631 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:44:01,631 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:44:01,632 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:44:01,633 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:44:01,633 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:44:01,634 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:44:01,635 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:44:01,636 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:44:01,637 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:44:01,637 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:44:01,638 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:44:01,638 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:44:01,641 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:44:01,645 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_124401.log
2025-07-30 12:44:01,646 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:44:01,647 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:44:01,647 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:44:01,648 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:44:01,648 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:44:01,648 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:44:01,649 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 20:44:01
2025-07-30 12:44:01,651 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:44:01,652 - __main__ - INFO - 📅 启动时间: 2025-07-30 20:44:01
2025-07-30 12:44:01,653 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:44:01,658 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:44:02,190 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:44:02,191 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:44:02,722 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:44:02,722 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:44:02,723 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:44:02,724 - __main__ - INFO - ✅ 找到 2 个可用微信窗口
2025-07-30 12:44:02,724 - __main__ - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 12:44:02,725 - __main__ - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:44:02,725 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 12:44:02,726 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 12:44:02,729 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 263568)
2025-07-30 12:44:02,737 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 12:44:03,038 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 9045874)
2025-07-30 12:44:03,038 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-30 12:44:03,039 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 12:44:03,039 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 12:44:03,039 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 12:44:03,040 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 12:44:03,040 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 12:44:03,040 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 12:44:03,967 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 12:44:03,968 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 12:44:03,968 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:44:03,968 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:44:03,969 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 12:44:03,969 - __main__ - INFO - 
============================================================
2025-07-30 12:44:03,969 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 12:44:03,969 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:44:03,970 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:44:03,970 - __main__ - INFO - ============================================================
2025-07-30 12:44:03,970 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:44:03,971 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:44:03,971 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:44:03,971 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:44:03,972 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 263568)
2025-07-30 12:44:03,972 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-07-30 12:44:03,972 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 12:44:04,973 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 12:44:06,074 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 12:44:06,075 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:44:06,075 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:44:06,380 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:44:06,380 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:44:06,381 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:44:06,381 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:44:06,381 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:44:06,382 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:44:06,382 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:44:06,382 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:44:06,383 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:44:06,383 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:44:06,585 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:44:06,585 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:44:06,587 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:44:06,888 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:44:06,889 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 12:44:06,889 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:44:07,890 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:44:07,890 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 12:44:07,891 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:44:07,891 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:44:07,891 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:44:07,892 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:44:07,892 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:44:07,892 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:44:08,093 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:44:08,094 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:44:10,467 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:44:10,467 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:44:10,468 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 12:44:12,827 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:44:13,028 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:44:13,029 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:44:15,400 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:44:15,401 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:44:15,401 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 12:44:18,053 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:44:18,254 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:44:18,254 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:44:20,622 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:44:20,623 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:44:20,623 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 12:44:22,187 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:44:22,388 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:44:22,388 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:44:24,766 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:44:24,767 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:44:24,767 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 12:44:26,714 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:44:26,915 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:44:26,915 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:44:29,300 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:44:29,300 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:44:29,301 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:44:29,301 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:44:29,301 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:44:29,303 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:44:29,303 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:44:29,304 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2231390, 进程: Weixin.exe)
2025-07-30 12:44:29,305 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:44:29,305 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:44:29,308 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:44:29,309 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 2231390)
2025-07-30 12:44:29,310 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2231390) - 增强版
2025-07-30 12:44:29,613 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:44:29,614 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:44:29,614 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:44:29,614 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:44:29,615 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 12:44:29,615 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:44:29,819 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 12:44:29,819 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:44:30,021 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:44:30,022 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:44:30,022 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 12:44:30,022 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 12:44:30,023 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 12:44:30,023 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:44:30,023 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 12:44:31,024 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 12:44:31,024 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:44:31,026 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:44:31,026 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:44:31,028 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2231390, 进程: Weixin.exe)
2025-07-30 12:44:31,029 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:44:31,029 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:44:31,032 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:44:31,033 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 12:44:31,033 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 12:44:31,033 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 12:44:31,033 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:44:31,034 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:44:31,341 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:44:31,342 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:44:31,342 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:44:31,342 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:44:31,343 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:44:31,343 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:44:31,344 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:44:31,344 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:44:31,344 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:44:31,344 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:44:31,546 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:44:31,547 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:44:31,548 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:44:31,849 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:44:31,850 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 12:44:31,850 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 12:44:32,851 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 12:44:32,851 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 12:44:32,852 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 12:44:32,854 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_124432.log
2025-07-30 12:44:32,855 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:44:32,855 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:44:32,856 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:44:32,856 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 12:44:32,857 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 12:44:32,857 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 12:44:32,859 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 12:44:32,859 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 12:44:32,860 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 12:44:32,860 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 12:44:32,860 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 12:44:32,860 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 12:44:32,861 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 12:44:32,862 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:44:32,863 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2231390
2025-07-30 12:44:32,863 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2231390) - 增强版
2025-07-30 12:44:33,171 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:44:33,172 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:44:33,172 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:44:33,173 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:44:33,173 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 12:44:33,173 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:44:33,174 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 12:44:33,174 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:44:33,376 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:44:33,376 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:44:33,380 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2231390 (API返回: None)
2025-07-30 12:44:33,681 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:44:33,681 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 12:44:33,681 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 12:44:33,682 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 12:44:33,683 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:44:33,684 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 12:44:33,684 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 12:44:33,688 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 12:44:33,689 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 12:44:34,190 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 12:44:34,191 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:44:34,443 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2901 个
2025-07-30 12:44:34,444 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2901 个 (总计: 3135 个)
2025-07-30 12:44:34,444 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 12:44:34,445 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 12:44:34,446 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:44:34,446 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:44:34,447 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 12:44:34,447 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2901
2025-07-30 12:44:34,447 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13580982528 (钟建强)
2025-07-30 12:44:34,449 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:44:34,450 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:44:41,025 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13580982528
2025-07-30 12:44:41,026 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 12:44:41,026 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13580982528 执行添加朋友操作...
2025-07-30 12:44:41,026 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 12:44:41,027 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 12:44:41,028 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:44:41,029 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 12:44:41,033 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 12:44:41,034 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 12:44:41,035 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 12:44:41,036 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 12:44:41,036 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 12:44:41,037 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 12:44:41,038 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 12:44:41,038 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 12:44:41,055 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 12:44:41,060 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:44:41,063 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:44:41,065 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 12:44:41,071 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 12:44:41,073 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 12:44:41,574 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 12:44:41,575 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 12:44:41,640 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差46.07, 边缘比例0.0618
2025-07-30 12:44:41,649 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_124441.png
2025-07-30 12:44:41,652 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 12:44:41,653 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 12:44:41,654 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 12:44:41,658 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 12:44:41,659 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 12:44:41,666 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_124441.png
2025-07-30 12:44:41,667 - WeChatAutoAdd - INFO - 底部区域原始检测到 51 个轮廓
2025-07-30 12:44:41,668 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,321), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 12:44:41,672 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=321 (距底部154像素区域)
2025-07-30 12:44:41,674 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 12:44:41,675 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,321), 尺寸128x30
2025-07-30 12:44:41,676 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,321), 尺寸128x30
2025-07-30 12:44:41,677 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,273), 尺寸6x6, 长宽比1.00, 面积36
2025-07-30 12:44:41,680 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,272), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,682 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,270), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 12:44:41,683 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,258), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 12:44:41,684 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,257), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:44:41,684 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,256), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 12:44:41,685 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,256), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,688 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,255), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:44:41,691 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 12:44:41,692 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,254), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 12:44:41,695 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,700 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,701 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,252), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,702 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,252), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:44:41,703 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,703 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,704 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 12:44:41,705 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,250), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:44:41,707 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,707 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,248), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 12:44:41,709 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,248), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 12:44:41,710 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,248), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 12:44:41,712 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:44:41,716 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:44:41,718 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=65.3 (阈值:60)
2025-07-30 12:44:41,719 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:44:41,720 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=66.7 (阈值:60)
2025-07-30 12:44:41,721 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,247), 尺寸34x35, 长宽比0.97, 面积1190
2025-07-30 12:44:41,721 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,240), 尺寸11x3, 长宽比3.67, 面积33
2025-07-30 12:44:41,722 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:44:41,724 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:44:41,725 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,239), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:44:41,725 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:44:41,726 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,239), 尺寸5x2, 长宽比2.50, 面积10
2025-07-30 12:44:41,727 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,731 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,737 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,238), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:44:41,738 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,235), 尺寸1x7, 长宽比0.14, 面积7
2025-07-30 12:44:41,740 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,235), 尺寸3x8, 长宽比0.38, 面积24
2025-07-30 12:44:41,741 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,234), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:44:41,742 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,233), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:44:41,743 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,233), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:44:41,750 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,232), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 12:44:41,751 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,231), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 12:44:41,753 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(223,230), 尺寸12x12, 长宽比1.00, 面积144
2025-07-30 12:44:41,755 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,230), 尺寸11x11, 长宽比1.00, 面积121
2025-07-30 12:44:41,758 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=77.7 (阈值:60)
2025-07-30 12:44:41,760 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(265,229), 尺寸14x14, 长宽比1.00, 面积196
2025-07-30 12:44:41,762 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,229), 尺寸28x15, 长宽比1.87, 面积420
2025-07-30 12:44:41,766 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,229), 尺寸26x14, 长宽比1.86, 面积364
2025-07-30 12:44:41,767 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=98.7 (阈值:60)
2025-07-30 12:44:41,768 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,229), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 12:44:41,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,229), 尺寸5x5, 长宽比1.00, 面积25
2025-07-30 12:44:41,770 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 12:44:41,771 - WeChatAutoAdd - INFO - 底部区域找到 7 个按钮候选
2025-07-30 12:44:41,772 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=321, 很可能是'添加到通讯录'按钮
2025-07-30 12:44:41,773 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 336), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 12:44:41,774 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 12:44:41,784 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_124441.png
2025-07-30 12:44:41,786 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 12:44:41,787 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 12:44:42,088 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 336) -> 屏幕坐标(1364, 336)
2025-07-30 12:44:42,857 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 12:44:42,858 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 12:44:42,859 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:44:42,860 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:44:42,860 - modules.wechat_auto_add_simple - INFO - ✅ 13580982528 添加朋友操作执行成功
2025-07-30 12:44:42,861 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:44:42,862 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:44:42,863 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 12:44:44,864 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 12:44:44,865 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 12:44:44,865 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 12:44:44,865 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:44:44,866 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:44:44,866 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:44:44,866 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:44:44,867 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:44:44,867 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 12:44:44,867 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13580982528
2025-07-30 12:44:44,871 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 12:44:44,872 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:44:44,872 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:44:44,874 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 12:44:44,875 - modules.friend_request_window - INFO -    📱 phone: '13580982528'
2025-07-30 12:44:44,875 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 12:44:44,876 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 12:44:45,385 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 12:44:45,385 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 12:44:45,386 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 12:44:45,386 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:44:45,387 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13580982528
2025-07-30 12:44:45,388 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 12:44:45,388 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:44:45,390 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 12:44:45,390 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 12:44:45,391 - modules.friend_request_window - INFO -    📱 手机号码: 13580982528
2025-07-30 12:44:45,391 - modules.friend_request_window - INFO -    🆔 准考证: 014325110149
2025-07-30 12:44:45,392 - modules.friend_request_window - INFO -    👤 姓名: 钟建强
2025-07-30 12:44:45,392 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 12:44:45,393 - modules.friend_request_window - INFO -    📝 备注格式: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:44:45,393 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:44:45,393 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:44:45,393 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 12:44:45,395 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8980966, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 12:44:45,396 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8980966)
2025-07-30 12:44:45,397 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 12:44:45,398 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 12:44:45,399 - modules.friend_request_window - INFO - 🔄 激活窗口: 8980966
2025-07-30 12:44:46,102 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 12:44:46,102 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 12:44:46,103 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 12:44:46,103 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 12:44:46,103 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 12:44:46,104 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:44:46,104 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 12:44:46,104 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:44:46,104 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 12:44:46,105 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 12:44:46,105 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 12:44:46,105 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 12:44:46,105 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 12:44:46,106 - modules.friend_request_window - INFO -    📝 remark参数: '014325110149-钟建强-2025-07-30 20:44:45' (类型: <class 'str'>, 长度: 36)
2025-07-30 12:44:46,106 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 12:44:46,106 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:44:46,106 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 12:44:46,107 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 12:44:46,107 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 12:44:46,107 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 12:44:46,107 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 12:44:46,108 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 12:44:46,109 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 12:44:47,031 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 12:44:52,272 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 12:44:52,273 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 12:44:52,273 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 12:44:52,273 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 12:44:52,275 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '1. 微信1窗口被系统检测为频繁点击操作
2. 点击微信1窗口的关闭按钮后，窗口被最小化（而非完全关...' (前50字符)
2025-07-30 12:44:52,586 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:44:52,587 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:44:53,489 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:44:53,499 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:44:53,499 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 12:44:53,499 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 12:44:53,500 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 12:44:53,501 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 12:44:54,001 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 12:44:54,002 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 12:44:54,002 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 12:44:54,002 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 12:44:54,002 - modules.friend_request_window - INFO -    📝 内容: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:44:54,003 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 12:44:54,003 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110149-\xe9\x92\x9f\xe5\xbb\xba\xe5\xbc\xba-2025-07-30 20:44:45'
2025-07-30 12:44:54,003 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 12:44:54,915 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 12:45:00,158 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 12:45:00,158 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 12:45:00,158 - modules.friend_request_window - INFO -    📝 原始文本: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:45:00,159 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 12:45:00,159 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '1. 微信1窗口被系统检测为频繁点击操作
2. 点击微信1窗口的关闭按钮后，窗口被最小化（而非完全关...' (前50字符)
2025-07-30 12:45:00,468 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:45:00,468 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:45:01,371 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:45:01,382 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:45:01,383 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:45:01,383 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 12:45:01,384 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:45:01,384 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 12:45:01,885 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:45:01,885 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 12:45:01,886 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 12:45:01,886 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 12:45:01,886 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 12:45:01,886 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 12:45:01,887 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 12:45:02,687 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 12:45:02,688 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 12:45:02,688 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 12:45:03,298 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:45:03,299 - modules.friend_request_window - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:45:03,299 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 12:45:03,299 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 12:45:03,300 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 12:45:03,801 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 12:45:03,803 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 12:45:03,804 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 12:45:03,804 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 12:45:03,804 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 12:45:03,805 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 12:45:03,805 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 12:45:03,805 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 12:45:03,805 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 12:45:03,806 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 12:45:03,806 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 12:45:03,806 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 12:45:03,807 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 12:45:03,808 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 12:45:03,808 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 12:45:03,809 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 12:45:03,809 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 12:45:03,810 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 12:45:03,816 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 12:45:03,816 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 12:45:03,817 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 12:45:04,321 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 12:45:04,322 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 12:45:04,322 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 12:45:04,322 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 12:45:04,323 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 12:45:04,323 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 12:45:04,323 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 12:45:04,324 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 12:45:05,231 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 12:45:05,231 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 12:45:05,231 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 12:45:05,231 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 12:45:05,248 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 12:45:05,249 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 12:45:06,049 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 12:45:06,050 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 12:45:06,050 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 12:45:06,050 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 12:45:06,051 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 12:45:06,051 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 12:45:06,052 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 12:45:06,052 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 12:45:07,553 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 12:45:07,553 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 12:45:07,554 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 12:45:07,554 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 12:45:09,555 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 12:45:09,556 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:45:09,556 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 12:45:09,556 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:09,558 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:09,558 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:09,560 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 12:45:09,560 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 12:45:09,561 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 12:45:09,579 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 12:45:09,580 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 12:45:09,580 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 12:45:09,581 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 263568
2025-07-30 12:45:09,582 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:45:09,582 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:45:09,582 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 12:45:10,110 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:45:10,111 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:45:10,111 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:45:10,111 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:45:10,112 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:45:10,112 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:45:10,112 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:45:10,113 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:45:10,113 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:45:10,113 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:45:10,315 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:45:10,316 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:45:10,316 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 12:45:10,316 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 12:45:10,317 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 12:45:10,317 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 12:45:10,317 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 12:45:12,318 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 12:45:12,319 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:45:12,319 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:45:12,319 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 12:45:12,320 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 12:45:12,320 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 12:45:12,320 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:45:12,321 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:45:12,321 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:45:12,321 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:45:12,321 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:45:12,322 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:45:12,523 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:45:12,523 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:45:14,896 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:45:14,896 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:45:14,896 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 12:45:16,937 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:45:17,172 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:45:17,214 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:45:19,598 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:45:19,598 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:45:19,598 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 12:45:21,413 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:45:21,614 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:45:21,615 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:45:23,997 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:45:23,998 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:45:24,015 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 12:45:25,621 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:45:25,822 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:45:25,823 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:45:28,194 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:45:28,195 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:45:28,195 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 12:45:29,847 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:45:30,048 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:45:30,049 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:45:32,429 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:45:32,429 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:45:32,431 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:45:32,432 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:45:32,433 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:45:32,433 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:32,434 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:32,435 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:32,436 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:32,437 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:32,440 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:32,943 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:32,945 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:32,945 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:32,946 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:32,947 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:32,949 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:33,450 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:33,451 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:33,452 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:33,453 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:33,453 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:33,455 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:33,956 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:33,958 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:33,960 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:33,961 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:33,961 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:33,965 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:34,466 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:34,468 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:34,469 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:34,470 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:34,470 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:34,472 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:34,982 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:34,990 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:35,099 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:35,106 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:35,107 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:35,136 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:35,636 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:35,638 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:35,638 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:35,639 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:35,639 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:35,641 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:36,144 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:36,146 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:36,146 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:36,147 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:36,147 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:36,149 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:36,649 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:36,651 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:36,651 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:36,652 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:36,653 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:36,655 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:37,156 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:37,157 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:37,158 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:37,159 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:37,159 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:37,161 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:37,662 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:37,663 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:37,663 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:37,664 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:37,665 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:37,666 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:38,167 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:38,168 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:38,168 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:38,169 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:38,170 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:38,171 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:38,672 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:38,673 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:38,676 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:38,679 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:38,679 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:38,682 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:39,183 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:39,184 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:39,185 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:39,186 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:39,186 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:39,188 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:39,689 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:39,690 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:39,690 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:39,692 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:39,692 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:39,694 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:40,194 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:40,196 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:40,196 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:40,197 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:40,197 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:40,199 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:40,700 - modules.window_manager - WARNING - ⚠️ 在8秒内未找到添加朋友窗口
2025-07-30 12:45:40,700 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未在指定时间内出现
2025-07-30 12:45:40,701 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未出现，但点击操作已执行
2025-07-30 12:45:40,701 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:45:40,701 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 12:45:41,702 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 12:45:41,749 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:45:41,756 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:45:42,310 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:42,320 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:45:42,323 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:45:42,327 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:45:42,333 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:45:42,336 - modules.main_interface - WARNING - ⚠️ 未找到添加朋友窗口
2025-07-30 12:45:42,338 - modules.main_interface - WARNING - ⚠️ [主界面操作] 添加朋友窗口验证失败，但继续执行
2025-07-30 12:45:42,339 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 12:45:42,339 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 12:45:42,340 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 12:45:42,341 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 12:45:43,370 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 12:45:43,371 - modules.friend_request_window - WARNING - ⚠️ 未找到添加朋友窗口，尝试等待窗口出现...
2025-07-30 12:45:45,375 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 12:45:45,376 - modules.friend_request_window - ERROR - ❌ 仍未找到添加朋友窗口，位置同步失败
2025-07-30 12:45:45,376 - modules.friend_request_window - WARNING - ⚠️ 添加朋友窗口位置同步失败，但继续执行
2025-07-30 12:45:45,376 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 12:45:45,377 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 12:45:45,377 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 12:45:45,377 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 12:45:45,377 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 12:45:45,378 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 12:45:45,378 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 12:45:45,378 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 12:45:45,379 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 12:45:45,379 - modules.friend_request_window - INFO -    📝 备注信息: '014325110149-钟建强-2025-07-30 20:44:45'
2025-07-30 12:45:45,880 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 12:45:45,881 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:45:45,881 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:45:45,882 - modules.wechat_auto_add_simple - INFO - ✅ 13580982528 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 12:45:45,882 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13580982528
2025-07-30 12:45:45,883 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:45:45,884 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:45:49,618 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2901
2025-07-30 12:45:49,618 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13250591024 (曾勇)
2025-07-30 12:45:49,619 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:45:49,619 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:45:56,204 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13250591024
2025-07-30 12:45:56,205 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 12:45:56,205 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13250591024 执行添加朋友操作...
2025-07-30 12:45:56,206 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 12:45:56,206 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 12:45:56,207 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:45:56,208 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 12:45:56,213 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 12:45:56,215 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 12:45:56,218 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 12:45:56,221 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 12:45:56,221 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 12:45:56,222 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 12:45:56,222 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 12:45:56,222 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 12:45:56,229 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 12:45:56,231 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:45:56,233 - WeChatAutoAdd - INFO - 共找到 2 个微信窗口
2025-07-30 12:45:56,239 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-07-30 12:45:56,249 - WeChatAutoAdd - WARNING - 方法1截图失败: Error code from Windows: 18 - 没有更多文件。, 尝试方法2
2025-07-30 12:45:56,864 - WeChatAutoAdd - INFO - 使用win32方法截取窗口: 
2025-07-30 12:45:56,868 - WeChatAutoAdd - INFO - win32窗口尺寸: 1x1, 位置: (0, 0)
2025-07-30 12:45:56,870 - WeChatAutoAdd - WARNING - win32 BitBlt操作可能失败
2025-07-30 12:45:56,871 - WeChatAutoAdd - WARNING - 截图可能为纯色图像，标准差: 0.0
2025-07-30 12:45:56,872 - WeChatAutoAdd - WARNING - win32截图内容验证失败
2025-07-30 12:45:56,878 - WeChatAutoAdd - INFO - 保存win32截图: screenshots\window_capture_win32_20250730_124556.png
2025-07-30 12:45:56,880 - WeChatAutoAdd - INFO - win32截图成功
2025-07-30 12:45:56,882 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 12:45:56,884 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 12:45:56,884 - WeChatAutoAdd - INFO - 截图尺寸: 1x1
2025-07-30 12:45:56,886 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=0-1 (高度:1)
2025-07-30 12:45:56,890 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_124556.png
2025-07-30 12:45:56,895 - WeChatAutoAdd - INFO - 底部区域原始检测到 0 个轮廓
2025-07-30 12:45:56,896 - WeChatAutoAdd - WARNING - 底部245像素区域未找到符合条件的按钮
2025-07-30 12:45:56,897 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-07-30 12:45:56,898 - WeChatAutoAdd - ERROR - 无法找到'添加到通讯录'按钮，结束当前流程
2025-07-30 12:45:56,900 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:45:56,900 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:45:56,900 - modules.wechat_auto_add_simple - ERROR - ❌ 13250591024 添加朋友操作执行失败
2025-07-30 12:45:56,901 - modules.wechat_auto_add_simple - WARNING - ⚠️ 添加朋友失败: 13250591024 - 添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮
2025-07-30 12:45:56,902 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:45:56,902 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:45:58,630 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 12:45:58,630 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 12:45:58,631 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 12:45:58,632 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 12:45:58,633 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 12:45:58,633 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 12:45:58,633 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 12:45:58,634 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 12:45:58,634 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 12:45:58,634 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 12:45:58,634 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 12:45:58,635 - __main__ - INFO - � 更新全局进度：已处理 2/2899 个联系人
2025-07-30 12:45:58,636 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 12:46:01,636 - __main__ - INFO - 
============================================================
2025-07-30 12:46:01,637 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 12:46:01,637 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 9045874)
2025-07-30 12:46:01,637 - __main__ - INFO - 📊 全局进度：已处理 2/2899 个联系人
2025-07-30 12:46:01,638 - __main__ - INFO - ============================================================
2025-07-30 12:46:01,638 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 12:46:01,638 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 9045874)
2025-07-30 12:46:01,638 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:46:01,639 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 12:46:01,639 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 9045874)
2025-07-30 12:46:01,641 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-30 12:46:01,642 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 12:46:02,643 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 12:46:02,643 - __main__ - WARNING - ⚠️ SetForegroundWindow 异常: (0, 'SetForegroundWindow', 'No error message is available')
2025-07-30 12:46:03,746 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 12:46:03,746 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 9045874
2025-07-30 12:46:03,747 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 9045874) - 增强版
2025-07-30 12:46:04,156 - modules.window_manager - INFO - ✅ 临时置顶策略成功
2025-07-30 12:46:04,157 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:46:04,158 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:46:04,158 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:46:04,159 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:46:04,159 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:46:04,159 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:46:04,160 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:46:04,160 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:46:04,160 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:46:04,362 - modules.window_manager - INFO - ✅ 窗口可见且非最小化（部分成功）
2025-07-30 12:46:04,363 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:46:04,365 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 9045874 (API返回: None)
2025-07-30 12:46:04,666 - modules.window_manager - WARNING - ⚠️ 窗口可能未完全置于最前面 (当前前台: 2297084)
2025-07-30 12:46:04,666 - __main__ - WARNING - ⚠️ 窗口激活可能不完全，但继续执行 (当前前台: 2297084)
2025-07-30 12:46:04,667 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:46:05,667 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:46:05,668 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 12:46:05,668 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:46:05,668 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:46:05,669 - modules.main_interface - WARNING - ⚠️ 当前前台窗口不是微信窗口: 'main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code' (类名: Chrome_WidgetWin_1)
2025-07-30 12:46:05,669 - modules.main_interface - INFO - 🔄 尝试查找并激活微信窗口...
2025-07-30 12:46:05,669 - modules.main_interface - INFO - 🔄 使用已初始化的window_manager查找并激活微信窗口...
2025-07-30 12:46:05,669 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:46:05,671 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:46:05,671 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:46:06,234 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:46:06,330 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:46:06,362 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:46:06,370 - modules.main_interface - INFO - 🎯 找到微信窗口: 微信 (类名: Qt51514QWindowIcon, 句柄: 263568)
2025-07-30 12:46:06,371 - modules.main_interface - INFO - 🚀 使用window_manager激活窗口（包含位置调整）...
2025-07-30 12:46:06,393 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:46:06,728 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:46:06,729 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:46:06,729 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:46:06,730 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:46:06,730 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:46:06,730 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:46:06,731 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:46:06,731 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:46:06,732 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:46:06,732 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:46:06,934 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:46:06,934 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:46:06,934 - modules.main_interface - INFO - ✅ 微信窗口激活和位置调整成功
2025-07-30 12:46:07,935 - modules.main_interface - INFO - ✅ 微信窗口已激活
2025-07-30 12:46:07,935 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:46:07,936 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:46:08,137 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:46:08,137 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:46:10,995 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:46:11,064 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:46:11,128 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 12:46:13,369 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:46:13,716 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:46:13,879 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:46:16,850 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:46:16,947 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:46:16,984 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 12:46:19,970 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:46:20,229 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:46:20,262 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
