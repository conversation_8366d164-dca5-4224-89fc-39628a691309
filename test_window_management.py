#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口管理测试脚本
用于测试修复后的窗口关闭和切换功能
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from modules.frequency_error_handler import FrequencyErrorHandler
from modules.window_manager import WeChatWindowManager
import win32gui

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_window_blacklist():
    """测试窗口黑名单功能"""
    print("🧪 测试窗口黑名单功能...")
    
    # 创建频率错误处理器
    handler = FrequencyErrorHandler()
    
    # 获取当前前台窗口作为测试
    current_hwnd = win32gui.GetForegroundWindow()
    current_title = win32gui.GetWindowText(current_hwnd)
    
    print(f"📋 当前窗口: {current_title} (句柄: {current_hwnd})")
    
    # 测试添加到黑名单
    print("🚫 添加到黑名单...")
    handler.add_window_to_blacklist(current_hwnd, current_title, "TEST", "测试添加")
    
    # 测试检查黑名单状态
    is_blacklisted = handler.is_window_blacklisted(current_hwnd, current_title)
    print(f"✅ 黑名单检查结果: {is_blacklisted}")
    
    # 获取黑名单信息
    blacklist_info = handler.get_window_blacklist_info(current_hwnd, current_title)
    if blacklist_info:
        print(f"📋 黑名单信息: {blacklist_info}")
    
    # 获取黑名单数量
    count = handler.get_blacklist_count()
    print(f"📊 黑名单窗口数量: {count}")
    
    return handler

def test_window_closing():
    """测试窗口关闭功能"""
    print("\n🧪 测试窗口关闭功能...")
    
    # 创建频率错误处理器和窗口管理器
    handler = FrequencyErrorHandler()
    window_manager = WeChatWindowManager(frequency_handler=handler)
    
    # 获取所有微信窗口
    wechat_windows = window_manager.get_all_wechat_windows()
    print(f"🔍 找到 {len(wechat_windows)} 个微信窗口")
    
    for i, window in enumerate(wechat_windows):
        hwnd = window.get('hwnd')
        title = window.get('title', 'Unknown')
        print(f"  窗口 {i+1}: {title} (句柄: {hwnd})")
        
        # 检查是否在黑名单中
        is_blacklisted = handler.is_window_blacklisted(hwnd, title)
        status = "🚫 黑名单" if is_blacklisted else "✅ 正常"
        print(f"    状态: {status}")
    
    return wechat_windows

def test_enhanced_window_closing(window_hwnd: int):
    """测试增强的窗口关闭方法"""
    print(f"\n🧪 测试增强的窗口关闭方法 (窗口: {window_hwnd})...")
    
    # 创建频率错误处理器
    handler = FrequencyErrorHandler()
    
    # 测试关闭方法
    print("🔄 尝试关闭窗口...")
    success = handler._close_window(window_hwnd)
    
    if success:
        print("✅ 窗口关闭成功")
    else:
        print("❌ 窗口关闭失败")
    
    # 验证窗口状态
    try:
        if win32gui.IsWindow(window_hwnd):
            is_visible = win32gui.IsWindowVisible(window_hwnd)
            is_iconic = win32gui.IsIconic(window_hwnd)
            print(f"📊 窗口状态: 存在=True, 可见={is_visible}, 最小化={is_iconic}")
        else:
            print("📊 窗口状态: 已不存在")
    except:
        print("📊 窗口状态: 检查失败（可能已关闭）")

def main():
    """主测试函数"""
    print("🚀 窗口管理功能测试开始")
    print("=" * 50)
    
    setup_logging()
    
    try:
        # 测试1: 黑名单功能
        handler = test_window_blacklist()
        
        # 测试2: 窗口扫描和状态检查
        wechat_windows = test_window_closing()
        
        # 测试3: 如果有微信窗口，测试关闭功能（仅测试，不实际关闭）
        if wechat_windows:
            print(f"\n⚠️ 注意: 以下测试仅模拟，不会实际关闭微信窗口")
            first_window = wechat_windows[0]
            hwnd = first_window.get('hwnd')
            title = first_window.get('title', 'Unknown')
            
            print(f"🎯 选择测试窗口: {title} (句柄: {hwnd})")
            
            # 这里只是演示，不实际执行关闭
            print("🔄 模拟关闭流程...")
            print("   1. 检查窗口状态")
            print("   2. 尝试WM_CLOSE消息")
            print("   3. 尝试系统关闭命令")
            print("   4. 如果失败，隐藏窗口")
            print("   5. 添加到黑名单")
            print("✅ 模拟完成")
        
        print("\n🎉 所有测试完成!")
        print("=" * 50)
        
        # 清理测试数据
        print("🧹 清理测试数据...")
        handler.clear_window_blacklist()
        print("✅ 清理完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
