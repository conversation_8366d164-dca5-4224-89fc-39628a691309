2025-07-30 12:31:49,758 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:31:49,758 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:31:49,759 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:31:49,760 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:31:49,760 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:31:49,761 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:31:49,762 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:31:49,763 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:31:49,764 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:31:49,764 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:31:49,764 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:31:49,765 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:31:49,767 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:31:49,771 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_123149.log
2025-07-30 12:31:49,772 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:31:49,773 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:31:49,774 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:31:49,775 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:31:49,776 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:31:49,778 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:31:49,779 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 20:31:49
2025-07-30 12:31:49,780 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:31:49,781 - __main__ - INFO - 📅 启动时间: 2025-07-30 20:31:49
2025-07-30 12:31:49,782 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:31:49,783 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:31:50,335 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:31:50,336 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:31:50,337 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:31:50,337 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:31:50,340 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:31:50,340 - __main__ - INFO - ✅ 找到 2 个可用微信窗口
2025-07-30 12:31:50,340 - __main__ - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 12:31:50,341 - __main__ - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:31:50,341 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 12:31:50,342 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 12:31:50,343 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 263568)
2025-07-30 12:31:50,344 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 12:31:50,645 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 9045874)
2025-07-30 12:31:50,645 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-30 12:31:50,646 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 12:31:50,646 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 12:31:50,648 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 12:31:50,648 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 12:31:50,648 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 12:31:50,649 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 12:31:51,547 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 12:31:51,547 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 12:31:51,548 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:31:51,548 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:31:51,549 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 12:31:51,549 - __main__ - INFO - 
============================================================
2025-07-30 12:31:51,549 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 12:31:51,550 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:31:51,550 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:31:51,551 - __main__ - INFO - ============================================================
2025-07-30 12:31:51,551 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:31:51,551 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:31:51,552 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:31:51,553 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:31:51,654 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:31:51,654 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:31:51,957 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:31:51,958 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:31:51,959 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:31:51,960 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:31:51,960 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:31:51,962 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:31:51,962 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:31:51,966 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:31:51,968 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:31:51,968 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:31:52,170 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:31:52,171 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:31:52,172 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:31:52,473 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:31:52,474 - __main__ - INFO - ✅ 步骤1：窗口管理完成（窗口已激活，位置已在批量移动阶段设置）
2025-07-30 12:31:52,474 - __main__ - INFO - ✅ 步骤 1 执行成功
