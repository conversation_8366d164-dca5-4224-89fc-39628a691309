2025-07-30 15:43:06,848 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:43:06,849 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:43:06,852 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:43:06,853 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:43:06,857 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:43:06,857 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:43:06,860 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:43:06,863 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 15:43:06,863 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:43:06,863 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:43:06,864 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:43:06,864 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:43:06,865 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:43:06,868 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:43:06,872 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_154306.log
2025-07-30 15:43:06,877 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:43:06,878 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:43:06,879 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:43:06,879 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 15:43:06,880 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 15:43:06,883 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 15:43:06,884 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 23:43:06
2025-07-30 15:43:06,885 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 15:43:06,888 - __main__ - INFO - 📅 启动时间: 2025-07-30 23:43:06
2025-07-30 15:43:06,889 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:43:06,890 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:43:07,428 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:07,428 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 527796, 进程: Weixin.exe)
2025-07-30 15:43:07,981 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:07,993 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 853980, 进程: Weixin.exe)
2025-07-30 15:43:08,007 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:43:08,009 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:43:08,009 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 15:43:08,009 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 15:43:08,010 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:43:08,010 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:43:08,010 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:43:08,011 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:43:08,011 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 527796)
2025-07-30 15:43:08,011 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 853980)
2025-07-30 15:43:08,012 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 15:43:08,013 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 15:43:08,014 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 527796)
2025-07-30 15:43:08,014 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 15:43:08,315 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 853980)
2025-07-30 15:43:08,315 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-07-30 15:43:08,316 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 15:43:08,316 - __main__ - INFO -   ✅ 成功移动: 2 个窗口
2025-07-30 15:43:08,317 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 15:43:08,317 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 15:43:08,318 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 15:43:08,318 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 15:43:09,307 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 15:43:09,307 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 15:43:09,307 - __main__ - INFO - 📋 待处理联系人数: 2838
2025-07-30 15:43:09,308 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 15:43:09,308 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2838
2025-07-30 15:43:09,308 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 2
2025-07-30 15:43:09,309 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:43:09,309 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:43:09,311 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:09,311 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 853980, 进程: Weixin.exe)
2025-07-30 15:43:09,311 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:09,312 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 527796, 进程: Weixin.exe)
2025-07-30 15:43:09,315 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:43:09,315 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:43:09,316 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 15:43:09,316 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 15:43:09,316 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:43:09,317 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:43:09,319 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:43:09,320 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:43:09,320 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 527796)
2025-07-30 15:43:09,321 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 853980)
2025-07-30 15:43:09,321 - __main__ - INFO - 
============================================================
2025-07-30 15:43:09,322 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 15:43:09,323 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 527796)
2025-07-30 15:43:09,324 - __main__ - INFO - 📊 全局进度：已处理 0/2838 个联系人（剩余 2838 个）
2025-07-30 15:43:09,324 - __main__ - INFO - ============================================================
2025-07-30 15:43:09,325 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:43:09,325 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 527796)
2025-07-30 15:43:09,326 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:43:09,327 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:43:09,330 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 527796)
2025-07-30 15:43:09,331 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-07-30 15:43:09,331 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 15:43:10,332 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:43:11,435 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:43:11,436 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 527796
2025-07-30 15:43:11,436 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 527796) - 增强版
2025-07-30 15:43:11,740 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:43:11,742 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:43:11,743 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:43:11,743 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:43:11,744 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:43:11,745 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:43:11,746 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:43:11,748 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:43:11,749 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:43:11,750 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:43:11,960 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:43:11,961 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:43:11,964 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 527796 (API返回: None)
2025-07-30 15:43:12,265 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:43:12,265 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:43:12,266 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:43:13,266 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:43:13,268 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:43:13,269 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:43:13,269 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:43:13,270 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:43:13,271 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:43:13,271 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:43:13,271 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:43:13,272 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:43:13,473 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:43:13,473 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:43:13,474 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:43:15,854 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:43:15,854 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:43:15,854 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 15:43:17,545 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:43:17,747 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:43:17,747 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:43:17,747 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:43:20,121 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:43:20,121 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:43:20,121 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 15:43:22,466 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:43:22,668 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:43:22,668 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:43:22,669 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:43:25,053 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:43:25,054 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:43:25,054 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 15:43:26,993 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:43:27,194 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:43:27,195 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:43:27,195 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:43:29,589 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:43:29,589 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:43:29,590 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 15:43:31,423 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:43:31,624 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:43:31,624 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:43:31,625 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:43:34,005 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:43:34,005 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:43:34,006 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:43:34,007 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:43:34,008 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:43:34,010 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:34,011 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 527796, 进程: Weixin.exe)
2025-07-30 15:43:34,013 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 986518, 进程: Weixin.exe)
2025-07-30 15:43:34,014 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:34,015 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 853980, 进程: Weixin.exe)
2025-07-30 15:43:34,018 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:43:34,023 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 986518)
2025-07-30 15:43:34,024 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 986518) - 增强版
2025-07-30 15:43:34,330 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:43:34,330 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:43:34,331 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:43:34,332 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:43:34,334 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:43:34,335 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:43:34,564 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:43:34,564 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:43:34,766 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:43:34,766 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:43:34,767 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:43:34,767 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:43:34,767 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:43:34,767 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:43:34,768 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:43:35,768 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:43:35,769 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:43:35,771 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:35,771 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 527796, 进程: Weixin.exe)
2025-07-30 15:43:35,772 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 986518, 进程: Weixin.exe)
2025-07-30 15:43:35,773 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:43:35,774 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 853980, 进程: Weixin.exe)
2025-07-30 15:43:35,777 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 15:43:35,778 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:43:35,779 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:43:35,779 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:43:35,779 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:43:35,780 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 527796
2025-07-30 15:43:35,780 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 527796) - 增强版
2025-07-30 15:43:36,088 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:43:36,089 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:43:36,089 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:43:36,090 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:43:36,090 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:43:36,090 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:43:36,091 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:43:36,091 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:43:36,091 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:43:36,091 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:43:36,294 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:43:36,294 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:43:36,296 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 527796 (API返回: None)
2025-07-30 15:43:36,596 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:43:36,597 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:43:36,597 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:43:36,597 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:43:37,598 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:43:37,599 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:43:37,600 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:43:37,604 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_154337.log
2025-07-30 15:43:37,605 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:43:37,606 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:43:37,606 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:43:37,606 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 15:43:37,607 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:43:37,607 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:43:37,610 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 15:43:37,610 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:43:37,610 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 15:43:37,611 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:43:37,612 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 15:43:37,612 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 15:43:37,613 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:43:37,614 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:43:37,614 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 986518
2025-07-30 15:43:37,614 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 986518) - 增强版
2025-07-30 15:43:37,922 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:43:37,922 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:43:37,923 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:43:37,924 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:43:37,924 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:43:37,925 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:43:37,925 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:43:37,926 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:43:38,127 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:43:38,128 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:43:38,130 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 986518 (API返回: None)
2025-07-30 15:43:38,433 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:43:38,433 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:43:38,433 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:43:38,434 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:43:38,435 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:43:38,435 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:43:38,436 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:43:38,442 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:43:38,442 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:43:38,986 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:43:38,986 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:43:39,284 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2838 个
2025-07-30 15:43:39,285 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2838 个 (总计: 3135 个)
2025-07-30 15:43:39,285 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:43:39,285 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:43:39,286 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:43:39,286 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:43:39,288 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:43:39,288 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:43:39,288 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2838
2025-07-30 15:43:39,289 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18585088999 (刘宣君)
2025-07-30 15:43:39,290 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:43:39,291 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:43:39,293 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:43:45,860 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18585088999
2025-07-30 15:43:45,860 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:43:45,860 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18585088999 执行添加朋友操作...
2025-07-30 15:43:45,861 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:43:45,861 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:43:45,862 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:43:45,863 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:43:45,869 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:43:45,870 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:43:45,872 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:43:45,874 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:43:45,876 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:43:45,877 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:43:45,881 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:43:45,885 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:43:45,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:43:45,896 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:43:45,897 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:43:45,910 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:43:45,920 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 15:43:45,927 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:43:46,428 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:43:46,430 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:43:46,509 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差46.62, 边缘比例0.0377
2025-07-30 15:43:46,517 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_154346.png
2025-07-30 15:43:46,521 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:43:46,522 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:43:46,525 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:43:46,526 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:43:46,527 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:43:46,532 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_154346.png
2025-07-30 15:43:46,536 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:43:46,543 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:43:46,547 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:43:46,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:43:46,551 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:43:46,554 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:43:46,556 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:43:46,558 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:43:46,568 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_154346.png
2025-07-30 15:43:46,574 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:43:46,576 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:43:46,582 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_154346.png
2025-07-30 15:43:46,650 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:43:46,652 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:43:46,652 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:43:46,653 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:43:46,954 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:43:47,735 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:43:47,736 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:43:47,737 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:43:47,737 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:43:47,740 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:43:47,740 - modules.wechat_auto_add_simple - INFO - ✅ 18585088999 添加朋友操作执行成功
2025-07-30 15:43:47,741 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:43:47,742 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:43:47,743 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:43:47,744 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:43:49,745 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:43:49,746 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:43:49,746 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:43:49,746 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:43:49,747 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:43:49,747 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:43:49,747 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:43:49,748 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:43:49,748 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:43:49,749 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18585088999
2025-07-30 15:43:49,754 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:43:49,755 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:43:49,756 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:43:49,756 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:43:49,757 - modules.friend_request_window - INFO -    📱 phone: '18585088999'
2025-07-30 15:43:49,758 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:43:49,759 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:43:50,322 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:43:50,323 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:43:50,324 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:43:50,325 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:43:50,327 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18585088999
2025-07-30 15:43:50,330 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:43:50,334 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:43:50,336 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:43:50,341 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:43:50,342 - modules.friend_request_window - INFO -    📱 手机号码: 18585088999
2025-07-30 15:43:50,343 - modules.friend_request_window - INFO -    🆔 准考证: 014325120078
2025-07-30 15:43:50,343 - modules.friend_request_window - INFO -    👤 姓名: 刘宣君
2025-07-30 15:43:50,344 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:43:50,344 - modules.friend_request_window - INFO -    📝 备注格式: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:43:50,345 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:43:50,345 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:43:50,345 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:43:50,347 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1313240, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:43:50,350 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1313240)
2025-07-30 15:43:50,350 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:43:50,351 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:43:50,352 - modules.friend_request_window - INFO - 🔄 激活窗口: 1313240
2025-07-30 15:43:51,055 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:43:51,055 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:43:51,056 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:43:51,056 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:43:51,057 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:43:51,058 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:43:51,059 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:43:51,059 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:43:51,059 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:43:51,059 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:43:51,060 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:43:51,060 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:43:51,060 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:43:51,061 - modules.friend_request_window - INFO -    📝 remark参数: '014325120078-刘宣君-2025-07-30 23:43:50' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:43:51,061 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:43:51,061 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:43:51,062 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:43:51,062 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:43:51,063 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:43:51,063 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:43:51,064 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:43:51,064 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:43:51,064 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:43:52,011 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:43:57,259 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:43:57,259 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:43:57,260 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:43:57,260 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:43:57,261 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '始增强点击操作，目标坐标: (1364, 252)

2025-07-30 15:31:00,758...' (前50字符)
2025-07-30 15:43:57,575 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:43:57,576 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:43:58,478 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:43:58,490 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:43:58,492 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:43:58,493 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:43:58,493 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:43:58,493 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:43:58,994 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:43:58,995 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:43:58,995 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:43:58,996 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:43:58,996 - modules.friend_request_window - INFO -    📝 内容: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:43:58,997 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:43:58,997 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120078-\xe5\x88\x98\xe5\xae\xa3\xe5\x90\x9b-2025-07-30 23:43:50'
2025-07-30 15:43:58,998 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:43:59,919 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:44:05,166 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:44:05,167 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:44:05,167 - modules.friend_request_window - INFO -    📝 原始文本: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:44:05,168 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:44:05,169 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '始增强点击操作，目标坐标: (1364, 252)

2025-07-30 15:31:00,758...' (前50字符)
2025-07-30 15:44:05,479 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:44:05,480 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:44:06,383 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:44:06,393 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:44:06,394 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:44:06,394 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:44:06,396 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:44:06,396 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:44:06,903 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:44:06,903 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:44:06,904 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:44:06,904 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:44:06,904 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:44:06,905 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:44:06,905 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:44:07,707 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:44:07,708 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:44:07,709 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:44:08,334 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:44:08,335 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:44:08,337 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:44:08,337 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:44:08,338 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:44:08,338 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:44:08,840 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:44:08,844 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:44:08,845 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:44:08,845 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:44:08,845 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:44:08,846 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:44:08,846 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:44:08,846 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:44:08,847 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:44:08,848 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:44:08,848 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:44:08,848 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:44:08,849 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:44:08,849 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:44:08,849 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:44:08,850 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:44:08,850 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:44:08,851 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 3
2025-07-30 15:44:08,852 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 15:44:08,852 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 6949924)
2025-07-30 15:44:08,857 - modules.friend_request_window - INFO - ✅ 频率错误窗口已强制添加到黑名单
2025-07-30 15:44:08,857 - modules.friend_request_window - INFO - 📊 当前黑名单窗口数量: 1
2025-07-30 15:44:08,858 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 15:44:08,858 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 15:44:09,360 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 15:44:09,360 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 15:44:09,361 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 15:44:09,361 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 15:44:09,361 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 15:44:09,362 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 15:44:09,362 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 15:44:09,362 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 15:44:10,267 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 15:44:10,268 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 15:44:10,269 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 15:44:10,270 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 15:44:10,291 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 15:44:10,291 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 15:44:11,092 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 15:44:11,093 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 15:44:11,093 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 15:44:11,093 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 15:44:11,094 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 15:44:11,094 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 15:44:11,095 - modules.friend_request_window - INFO - 🎯 强制使用坐标(700, 16)关闭微信主窗口
2025-07-30 15:44:11,095 - modules.friend_request_window - INFO -    激活窗口...
2025-07-30 15:44:11,596 - modules.friend_request_window - INFO -    使用坐标(700, 16)点击关闭按钮...
2025-07-30 15:44:11,598 - modules.friend_request_window - INFO - 📊 关闭前微信窗口数量: 2
2025-07-30 15:44:12,734 - modules.friend_request_window - INFO - 🔥 强制终止微信进程确保真正关闭...
2025-07-30 15:44:12,735 - modules.friend_request_window - INFO - 🔥 开始强制终止微信进程...
2025-07-30 15:44:12,735 - modules.friend_request_window - INFO - 📋 目标进程ID: 33396
2025-07-30 15:44:12,735 - modules.friend_request_window - INFO - 📋 进程名称: Weixin.exe
2025-07-30 15:44:12,736 - modules.friend_request_window - INFO - 🔄 方法1: 优雅终止进程...
2025-07-30 15:44:13,136 - modules.friend_request_window - INFO - ✅ 进程已优雅终止
2025-07-30 15:44:13,141 - modules.friend_request_window - INFO - ✅ 进程终止成功
2025-07-30 15:44:14,153 - modules.friend_request_window - INFO - 📊 关闭后微信窗口数量: 1
2025-07-30 15:44:14,154 - modules.friend_request_window - INFO - ✅ 第一次坐标点击+进程终止成功关闭微信主窗口
2025-07-30 15:44:14,154 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 15:44:14,155 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 15:44:14,155 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 15:44:16,156 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 15:44:16,157 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:44:16,157 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 15:44:16,158 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:44:16,160 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:44:16,160 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 853980, 进程: Weixin.exe)
2025-07-30 15:44:16,162 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:44:16,163 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 15:44:16,166 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 15:44:16,194 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 15:44:16,195 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 15:44:16,195 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 15:44:16,195 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 591318
2025-07-30 15:44:16,196 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:44:16,197 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 591318) - 增强版
2025-07-30 15:44:16,198 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 15:44:16,713 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:44:16,713 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:44:16,716 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 640x480, 面积=307200
2025-07-30 15:44:16,717 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:44:16,717 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:44:16,717 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:44:16,718 - modules.window_manager - INFO - 📏 当前窗口位置: (640, 276), 大小: 640x480
2025-07-30 15:44:16,718 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:44:16,718 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 15:44:17,026 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 240x82
2025-07-30 15:44:17,026 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:44:17,027 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(483, 568)
2025-07-30 15:44:17,027 - modules.window_manager - WARNING - ⚠️ 窗口验证失败 - 位置差异: (0, 0), 大小差异: (483, 568)
2025-07-30 15:44:17,027 - modules.window_manager - WARNING - ⚠️ 窗口大小和位置调整未达到预期效果
2025-07-30 15:44:17,028 - modules.window_manager - WARNING - ⚠️ 微信主窗口大小调整失败，尝试仅移动位置
2025-07-30 15:44:17,028 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 240x82
2025-07-30 15:44:17,028 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:44:17,029 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:44:17,029 - modules.window_manager - INFO - 📍 微信主窗口位置移动成功
2025-07-30 15:44:17,231 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:44:17,232 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:44:17,232 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 15:44:17,232 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 15:44:17,233 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 15:44:17,233 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 15:44:17,233 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 15:44:19,234 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 15:44:19,235 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:44:19,236 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:44:19,237 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 15:44:19,237 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 15:44:19,237 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 15:44:19,237 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:44:19,238 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:44:19,238 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:44:19,238 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:44:19,239 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:44:19,239 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:44:19,440 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:44:19,441 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:44:19,441 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:44:21,817 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:44:21,818 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:44:21,818 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 15:44:24,254 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:44:24,455 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:44:24,455 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:44:24,456 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:44:26,852 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:44:26,852 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:44:26,853 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 15:44:29,589 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:44:29,789 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:44:29,790 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:44:29,790 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:44:32,166 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:44:32,166 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:44:32,167 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.2 秒...
2025-07-30 15:44:34,365 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:44:34,566 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:44:34,566 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:44:34,567 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:44:36,951 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:44:36,951 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:44:36,951 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 15:44:39,635 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:44:39,836 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:44:39,837 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:44:39,837 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:44:42,216 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:44:42,216 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:44:42,217 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:44:42,218 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:44:42,219 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:44:42,219 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:44:42,221 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 528132, 进程: Weixin.exe)
2025-07-30 15:44:42,222 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:44:42,223 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 853980, 进程: Weixin.exe)
2025-07-30 15:44:42,229 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:44:42,231 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 528132)
2025-07-30 15:44:42,232 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 528132) - 增强版
2025-07-30 15:44:42,535 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:44:42,537 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:44:42,538 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:44:42,539 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:44:42,539 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:44:42,540 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:44:42,752 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:44:42,752 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:44:42,955 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:44:42,955 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:44:42,955 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:44:42,956 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:44:42,956 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:44:42,956 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:44:42,956 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:44:43,957 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:44:43,958 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:44:43,958 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:44:43,960 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 528132, 进程: Weixin.exe)
2025-07-30 15:44:43,960 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:44:43,961 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 853980, 进程: Weixin.exe)
2025-07-30 15:44:43,964 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:44:43,965 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:44:43,965 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:44:43,966 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:44:43,966 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 15:44:43,966 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 15:44:43,966 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 15:44:44,970 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 15:44:44,970 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 15:44:44,971 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 15:44:44,971 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 15:44:44,971 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 15:44:44,972 - modules.friend_request_window - INFO - 🔧 开始移动窗口 528132 到目标位置...
2025-07-30 15:44:45,776 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 15:44:45,777 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 15:44:45,777 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 15:44:45,777 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 15:44:45,779 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 15:44:45,780 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 15:44:45,780 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 15:44:45,781 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 15:44:45,781 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 15:44:45,781 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 15:44:45,781 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 15:44:45,782 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:44:45,783 - modules.friend_request_window - INFO -    📝 备注信息: '014325120078-刘宣君-2025-07-30 23:43:50'
2025-07-30 15:44:46,284 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 15:44:46,286 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:44:46,287 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:44:46,289 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:44:46,289 - modules.wechat_auto_add_simple - INFO - ✅ 18585088999 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 15:44:46,289 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18585088999
2025-07-30 15:44:46,291 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:44:46,292 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:44:46,294 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:44:50,083 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2838
2025-07-30 15:44:50,084 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15556311090 (郑智冉)
2025-07-30 15:44:50,084 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:44:50,085 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:44:50,086 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:44:56,677 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15556311090
2025-07-30 15:44:56,678 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:44:56,678 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15556311090 执行添加朋友操作...
2025-07-30 15:44:56,678 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:44:56,678 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:44:56,679 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:44:56,680 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:44:56,685 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:44:56,687 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:44:56,688 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:44:56,689 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:44:56,692 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:44:56,693 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:44:56,695 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:44:56,696 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:44:56,702 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:44:56,705 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:44:56,706 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:44:56,709 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 15:44:56,710 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:44:57,217 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:44:57,218 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:44:57,282 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.23, 边缘比例0.0374
2025-07-30 15:44:57,290 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_154457.png
2025-07-30 15:44:57,293 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:44:57,294 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:44:57,297 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:44:57,300 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:44:57,301 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:44:57,307 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_154457.png
2025-07-30 15:44:57,309 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:44:57,311 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:44:57,315 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:44:57,317 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:44:57,318 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:44:57,321 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:44:57,323 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:44:57,324 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:44:57,338 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_154457.png
2025-07-30 15:44:57,342 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:44:57,343 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:44:57,350 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_154457.png
2025-07-30 15:44:57,372 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:44:57,383 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:44:57,387 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:44:57,389 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:44:57,692 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:44:58,465 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:44:58,466 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:44:58,468 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:44:58,468 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:44:58,471 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:44:58,472 - modules.wechat_auto_add_simple - INFO - ✅ 15556311090 添加朋友操作执行成功
2025-07-30 15:44:58,473 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:44:58,473 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:44:58,475 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:44:58,476 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:45:00,478 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 申请添加朋友
2025-07-30 15:45:00,478 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:45:00,478 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:45:00,479 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:45:00,479 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:45:00,479 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:45:00,480 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:45:00,480 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:45:00,480 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:45:00,480 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15556311090
2025-07-30 15:45:00,481 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:45:00,481 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:45:00,482 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:45:00,482 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:45:00,482 - modules.friend_request_window - INFO -    📱 phone: '15556311090'
2025-07-30 15:45:00,482 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:45:00,483 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:45:01,079 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:45:01,079 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:45:01,080 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:45:01,080 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:45:01,081 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15556311090
2025-07-30 15:45:01,082 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:45:01,082 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:45:01,083 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:45:01,083 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:45:01,083 - modules.friend_request_window - INFO -    📱 手机号码: 15556311090
2025-07-30 15:45:01,083 - modules.friend_request_window - INFO -    🆔 准考证: 014325120080
2025-07-30 15:45:01,084 - modules.friend_request_window - INFO -    👤 姓名: 郑智冉
2025-07-30 15:45:01,084 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:45:01,084 - modules.friend_request_window - INFO -    📝 备注格式: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:01,084 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:45:01,085 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:01,085 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:45:01,086 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1771582, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:45:01,088 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1771582)
2025-07-30 15:45:01,089 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:45:01,090 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:45:01,091 - modules.friend_request_window - INFO - 🔄 激活窗口: 1771582
2025-07-30 15:45:01,794 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:45:01,796 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:45:01,797 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:45:01,797 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:45:01,798 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:45:01,798 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:45:01,798 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:45:01,799 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:45:01,799 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:45:01,799 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:45:01,800 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:45:01,800 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:45:01,800 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:45:01,801 - modules.friend_request_window - INFO -    📝 remark参数: '014325120080-郑智冉-2025-07-30 23:45:01' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:45:01,803 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:45:01,805 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:01,806 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:45:01,806 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:45:01,807 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:45:01,807 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:45:01,808 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:45:01,808 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:45:01,809 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:45:02,732 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:45:07,980 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:45:07,981 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:45:07,981 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:45:07,981 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:45:07,982 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '始增强点击操作，目标坐标: (1364, 252)

2025-07-30 15:31:00,758...' (前50字符)
2025-07-30 15:45:08,292 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:45:08,292 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:45:09,196 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:45:09,205 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:45:09,206 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:45:09,207 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:45:09,207 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:45:09,208 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:45:09,709 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:45:09,710 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:45:09,710 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:45:09,710 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:45:09,711 - modules.friend_request_window - INFO -    📝 内容: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:09,711 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:45:09,711 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120080-\xe9\x83\x91\xe6\x99\xba\xe5\x86\x89-2025-07-30 23:45:01'
2025-07-30 15:45:09,711 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:45:10,631 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:45:15,876 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:45:15,876 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:45:15,877 - modules.friend_request_window - INFO -    📝 原始文本: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:15,877 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:45:15,878 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '始增强点击操作，目标坐标: (1364, 252)

2025-07-30 15:31:00,758...' (前50字符)
2025-07-30 15:45:16,187 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:45:16,187 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:45:17,090 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:45:17,101 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:45:17,102 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:17,103 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:45:17,104 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:17,104 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:45:17,609 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120080-郑智冉-2025-07-30 23:45:01'
2025-07-30 15:45:17,621 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:45:17,622 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:45:17,623 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:45:17,624 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:45:17,624 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:45:17,625 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:45:18,440 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:45:18,440 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:45:18,441 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:45:19,049 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:45:19,051 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:45:19,055 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 15:45:19,055 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:45:19,055 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:45:19,056 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:45:19,575 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 15:45:19,793 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:45:19,795 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:45:19,795 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:45:19,795 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:45:19,796 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:45:19,796 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:45:19,796 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:45:19,796 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:45:19,797 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:45:19,797 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:45:19,797 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:45:19,798 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:45:19,798 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:45:19,799 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:45:19,799 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:45:19,800 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:45:19,800 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:45:19,802 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 3
2025-07-30 15:45:19,803 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 15:45:19,804 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 790098)
2025-07-30 15:45:19,810 - modules.friend_request_window - INFO - ✅ 频率错误窗口已强制添加到黑名单
2025-07-30 15:45:19,810 - modules.friend_request_window - INFO - 📊 当前黑名单窗口数量: 1
2025-07-30 15:45:19,811 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 15:45:19,812 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 15:45:20,314 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 15:45:20,315 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 15:45:20,315 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 15:45:20,316 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 15:45:20,316 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 15:45:20,317 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 15:45:20,317 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 15:45:20,317 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 15:45:21,249 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 15:45:21,249 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 15:45:21,250 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 15:45:21,250 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 15:45:21,263 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 15:45:21,264 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 15:45:22,065 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 15:45:22,065 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 15:45:22,066 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 15:45:22,066 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 15:45:22,067 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 15:45:22,068 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 15:45:22,068 - modules.friend_request_window - INFO - 🎯 强制使用坐标(700, 16)关闭微信主窗口
2025-07-30 15:45:22,068 - modules.friend_request_window - INFO -    激活窗口...
2025-07-30 15:45:22,569 - modules.friend_request_window - INFO -    使用坐标(700, 16)点击关闭按钮...
2025-07-30 15:45:22,572 - modules.friend_request_window - INFO - 📊 关闭前微信窗口数量: 2
2025-07-30 15:45:23,682 - modules.friend_request_window - INFO - 🔥 强制终止微信进程确保真正关闭...
2025-07-30 15:45:23,682 - modules.friend_request_window - INFO - 🔥 开始强制终止微信进程...
2025-07-30 15:45:23,683 - modules.friend_request_window - INFO - 📋 目标进程ID: 14952
2025-07-30 15:45:23,683 - modules.friend_request_window - INFO - 📋 进程名称: Weixin.exe
2025-07-30 15:45:23,684 - modules.friend_request_window - INFO - 🔄 方法1: 优雅终止进程...
2025-07-30 15:45:24,285 - modules.friend_request_window - INFO - ✅ 进程已优雅终止
2025-07-30 15:45:24,287 - modules.friend_request_window - INFO - ✅ 进程终止成功
2025-07-30 15:45:25,290 - modules.friend_request_window - INFO - 📊 关闭后微信窗口数量: 0
2025-07-30 15:45:25,290 - modules.friend_request_window - INFO - ✅ 第一次坐标点击+进程终止成功关闭微信主窗口
2025-07-30 15:45:25,291 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 15:45:25,291 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 15:45:25,291 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 15:45:27,292 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 15:45:27,293 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:45:27,294 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 15:45:27,294 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:45:27,297 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-30 15:45:27,298 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 15:45:27,299 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 15:45:27,325 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 15:45:27,328 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 15:45:27,329 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 15:45:27,329 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 1115678
2025-07-30 15:45:27,330 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:45:27,331 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1115678) - 增强版
2025-07-30 15:45:27,332 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 15:45:27,862 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:45:27,862 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:45:27,863 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 640x480, 面积=307200
2025-07-30 15:45:27,863 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:45:27,864 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:45:27,864 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:45:27,864 - modules.window_manager - INFO - 📏 当前窗口位置: (640, 276), 大小: 640x480
2025-07-30 15:45:27,865 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:45:27,865 - modules.window_manager - INFO - 🔄 尝试调整窗口大小和位置...
2025-07-30 15:45:28,170 - modules.window_manager - INFO - 📊 验证结果 - 当前位置: (0, 0), 实际大小: 240x82
2025-07-30 15:45:28,171 - modules.window_manager - INFO - 📊 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:45:28,171 - modules.window_manager - INFO - 📊 差异: 位置(0, 0), 大小(483, 568)
2025-07-30 15:45:28,172 - modules.window_manager - WARNING - ⚠️ 窗口验证失败 - 位置差异: (0, 0), 大小差异: (483, 568)
2025-07-30 15:45:28,172 - modules.window_manager - WARNING - ⚠️ 窗口大小和位置调整未达到预期效果
2025-07-30 15:45:28,172 - modules.window_manager - WARNING - ⚠️ 微信主窗口大小调整失败，尝试仅移动位置
2025-07-30 15:45:28,173 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 240x82
2025-07-30 15:45:28,173 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:45:28,173 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:45:28,174 - modules.window_manager - INFO - 📍 微信主窗口位置移动成功
2025-07-30 15:45:28,375 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:45:28,375 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:45:28,376 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 15:45:28,376 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 15:45:28,377 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 15:45:28,377 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 15:45:28,377 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 15:45:30,378 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 15:45:30,379 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:45:30,379 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:45:30,380 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 15:45:30,380 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 15:45:30,380 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 15:45:30,380 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:45:30,381 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:45:30,381 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:45:30,382 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:45:30,382 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:45:30,382 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:45:30,583 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:45:30,584 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:45:30,584 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:45:33,032 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:45:33,062 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:45:33,083 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 15:45:35,203 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:45:35,404 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:45:35,405 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:45:35,405 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:45:37,805 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:45:37,806 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:45:37,806 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 15:45:40,214 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:45:40,415 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:45:40,416 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:45:40,416 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:45:42,817 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:45:42,887 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:45:42,914 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
