2025-07-30 15:50:31,547 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:50:31,550 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:50:31,790 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:50:31,916 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-07-30 15:50:31,950 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 0 个
2025-07-30 15:50:31,985 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-30 15:50:31,999 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:50:32,005 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:50:32,034 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 15:50:32,039 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:50:32,055 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 15:50:32,055 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:50:32,063 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:50:32,064 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:50:32,065 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:50:32,072 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:50:32,087 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:50:32,106 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_155032.log
2025-07-30 15:50:32,115 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:50:32,116 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:50:32,117 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:50:32,118 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 15:50:32,122 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 15:50:32,123 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 15:50:32,137 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 23:50:32
2025-07-30 15:50:32,138 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 15:50:32,138 - __main__ - INFO - 📅 启动时间: 2025-07-30 23:50:32
2025-07-30 15:50:32,139 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:50:32,140 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:50:32,695 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:50:32,695 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:50:32,696 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:50:32,697 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:50:32,697 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 15:50:32,697 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 15:50:32,698 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:50:32,698 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:50:32,700 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:50:32,700 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:50:32,701 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:50:32,702 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 15:50:32,702 - __main__ - INFO - 📊 需要移动的窗口数量: 1
2025-07-30 15:50:32,703 - __main__ - INFO - 🔄 移动窗口 1/1: 微信 (句柄: 2426918)
2025-07-30 15:50:32,705 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 15:50:32,705 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 15:50:32,706 - __main__ - INFO -   ✅ 成功移动: 1 个窗口
2025-07-30 15:50:32,713 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 15:50:32,715 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 15:50:32,717 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 15:50:32,719 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 15:50:34,422 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 15:50:34,423 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 15:50:34,426 - __main__ - INFO - 📋 待处理联系人数: 2837
2025-07-30 15:50:34,426 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 15:50:34,427 - __main__ - INFO - 📊 总窗口数: 1, 总联系人数: 2837
2025-07-30 15:50:34,427 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 1
2025-07-30 15:50:34,428 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:50:34,429 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:50:34,433 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:50:34,435 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:50:34,437 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:50:34,448 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:50:34,451 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 15:50:34,452 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 15:50:34,452 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:50:34,454 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:50:34,454 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:50:34,455 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:50:34,467 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:50:34,469 - __main__ - INFO - 
============================================================
2025-07-30 15:50:34,469 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 1 轮)
2025-07-30 15:50:34,470 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:50:34,470 - __main__ - INFO - 📊 全局进度：已处理 0/2837 个联系人（剩余 2837 个）
2025-07-30 15:50:34,471 - __main__ - INFO - ============================================================
2025-07-30 15:50:34,471 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:50:34,471 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:50:34,472 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:50:34,477 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:50:34,479 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2426918)
2025-07-30 15:50:34,480 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-30 15:50:34,482 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 15:50:35,488 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:50:36,597 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:50:36,598 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 15:50:36,599 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 15:50:36,904 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:50:36,904 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:50:36,905 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:50:36,906 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:50:36,906 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:50:36,907 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:50:36,908 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:50:36,908 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:50:36,910 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:50:36,911 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:50:37,113 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:50:37,114 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:50:37,116 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 15:50:37,417 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:50:37,418 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:50:37,418 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:50:38,419 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:50:38,419 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:50:38,420 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:50:38,420 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:50:38,420 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:50:38,421 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:50:38,421 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:50:38,421 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:50:38,421 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:50:38,623 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:50:38,623 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:50:38,624 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:50:40,994 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:50:40,994 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:50:40,995 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 15:50:43,369 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:50:43,570 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:50:43,570 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:50:43,571 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:50:45,965 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:50:45,965 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:50:45,966 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 15:50:48,231 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:50:48,433 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:50:48,433 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:50:48,434 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:50:50,855 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:50:50,856 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:50:50,856 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 15:50:52,549 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:50:52,752 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:50:52,753 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:50:52,753 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:50:55,145 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:50:55,145 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:50:55,146 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 15:50:57,561 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:50:57,761 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:50:57,762 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:50:57,763 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:51:00,186 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:51:00,211 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:51:00,233 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:51:00,270 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:51:00,300 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:51:00,320 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:51:00,345 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:51:00,364 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:51:00,370 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:51:00,371 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1642110)
2025-07-30 15:51:00,374 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1642110) - 增强版
2025-07-30 15:51:00,678 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:51:00,678 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:51:00,679 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:51:00,679 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:51:00,680 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 15:51:00,680 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:51:00,891 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:51:00,892 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:51:01,094 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:51:01,094 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:51:01,094 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:51:01,095 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:51:01,095 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:51:01,095 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:51:01,095 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:51:02,096 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:51:02,097 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:51:02,098 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:51:02,099 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:51:02,101 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:51:02,103 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:51:02,104 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:51:02,104 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:51:02,108 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:51:02,108 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:51:02,109 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 15:51:02,110 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 15:51:02,419 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:51:02,420 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:51:02,420 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:51:02,421 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:51:02,421 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:51:02,421 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:51:02,422 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:51:02,423 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:51:02,423 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:51:02,424 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:51:02,626 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:51:02,626 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:51:02,628 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 15:51:02,929 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:51:02,930 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:51:02,930 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:51:02,930 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:51:03,931 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:51:03,932 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:51:03,933 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:51:03,935 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_155103.log
2025-07-30 15:51:03,936 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:51:03,937 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:51:03,938 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:51:03,939 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 15:51:03,940 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:51:03,940 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:51:03,943 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 15:51:03,945 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:51:03,951 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 15:51:03,952 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:51:03,952 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 15:51:03,953 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:51:03,954 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:51:03,954 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1642110
2025-07-30 15:51:03,959 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1642110) - 增强版
2025-07-30 15:51:04,267 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:51:04,268 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:51:04,269 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:51:04,269 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:51:04,270 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:51:04,270 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:51:04,271 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:51:04,271 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:51:04,474 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:51:04,474 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:51:04,477 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1642110 (API返回: None)
2025-07-30 15:51:04,778 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:51:04,779 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:51:04,779 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:51:04,779 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:51:04,780 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:51:04,781 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:51:04,781 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:51:04,789 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:51:04,789 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:51:05,352 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:51:05,353 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:51:05,649 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2837 个
2025-07-30 15:51:05,650 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2837 个 (总计: 3135 个)
2025-07-30 15:51:05,651 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:51:05,651 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:51:05,651 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:05,652 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:05,653 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:05,653 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:05,654 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:05,654 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:51:05,654 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2837
2025-07-30 15:51:05,656 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15556311090 (郑智冉)
2025-07-30 15:51:05,656 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:05,657 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:05,658 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:05,659 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:05,660 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:12,232 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15556311090
2025-07-30 15:51:12,233 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:51:12,233 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15556311090 执行添加朋友操作...
2025-07-30 15:51:12,233 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:51:12,234 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:51:12,234 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:51:12,235 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:51:12,242 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:51:12,243 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:51:12,244 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:51:12,244 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:51:12,244 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:51:12,245 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:51:12,245 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:51:12,246 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:51:12,249 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:51:12,251 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:51:12,257 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:51:12,259 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 15:51:12,260 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:51:12,820 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:51:12,823 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:51:12,899 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.20, 边缘比例0.0375
2025-07-30 15:51:12,908 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_155112.png
2025-07-30 15:51:12,910 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:51:12,911 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:51:12,913 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:51:12,914 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:51:12,915 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:51:12,919 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_155112.png
2025-07-30 15:51:12,921 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:51:12,924 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:51:12,936 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:51:12,940 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:51:12,953 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:51:12,964 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:51:12,971 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:51:12,983 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:51:13,000 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_155112.png
2025-07-30 15:51:13,013 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:51:13,020 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:51:13,030 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_155113.png
2025-07-30 15:51:13,103 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:51:13,104 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:51:13,106 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:51:13,107 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:51:13,409 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:51:14,190 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:51:14,192 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:51:14,193 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:14,193 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:14,195 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:14,198 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:14,198 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:14,199 - modules.wechat_auto_add_simple - INFO - ✅ 15556311090 添加朋友操作执行成功
2025-07-30 15:51:14,199 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:14,200 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:14,201 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:14,203 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:14,203 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:14,204 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:51:16,209 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:51:16,209 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:51:16,210 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:51:16,210 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:51:16,210 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:51:16,211 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:51:16,212 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:51:16,212 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:51:16,212 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:51:16,213 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15556311090
2025-07-30 15:51:16,224 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:51:16,228 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:51:16,228 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:51:16,231 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:51:16,231 - modules.friend_request_window - INFO -    📱 phone: '15556311090'
2025-07-30 15:51:16,232 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:51:16,232 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:51:16,814 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:51:16,814 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:51:16,814 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:51:16,815 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:51:16,816 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15556311090
2025-07-30 15:51:16,817 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:51:16,817 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:51:16,818 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:51:16,819 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:51:16,819 - modules.friend_request_window - INFO -    📱 手机号码: 15556311090
2025-07-30 15:51:16,823 - modules.friend_request_window - INFO -    🆔 准考证: 014325120080
2025-07-30 15:51:16,824 - modules.friend_request_window - INFO -    👤 姓名: 郑智冉
2025-07-30 15:51:16,828 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:51:16,836 - modules.friend_request_window - INFO -    📝 备注格式: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:16,836 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:51:16,837 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:16,839 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:51:16,841 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 8063046, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:51:16,851 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 8063046)
2025-07-30 15:51:16,851 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:51:16,854 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:51:16,855 - modules.friend_request_window - INFO - 🔄 激活窗口: 8063046
2025-07-30 15:51:17,559 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:51:17,559 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:51:17,560 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:51:17,560 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:51:17,560 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:51:17,561 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:51:17,561 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:51:17,561 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:51:17,561 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:51:17,562 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:51:17,562 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:51:17,562 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:51:17,562 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:51:17,563 - modules.friend_request_window - INFO -    📝 remark参数: '014325120080-郑智冉-2025-07-30 23:51:16' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:51:17,563 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:51:17,563 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:17,564 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:51:17,564 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:51:17,564 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:51:17,564 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:51:17,564 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:51:17,565 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:51:17,565 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:51:18,499 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:51:23,769 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:51:23,770 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:51:23,771 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:51:23,771 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:51:23,773 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 15:51:24,084 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:51:24,085 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:51:24,988 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:51:24,998 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:51:24,999 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:51:24,999 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:51:25,000 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:51:25,000 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:51:25,501 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:51:25,501 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:51:25,502 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:51:25,502 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:51:25,502 - modules.friend_request_window - INFO -    📝 内容: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:25,503 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:51:25,503 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120080-\xe9\x83\x91\xe6\x99\xba\xe5\x86\x89-2025-07-30 23:51:16'
2025-07-30 15:51:25,504 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:51:26,426 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:51:31,715 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:51:31,716 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:51:31,716 - modules.friend_request_window - INFO -    📝 原始文本: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:31,716 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:51:31,717 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 15:51:32,027 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:51:32,027 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:51:32,930 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:51:32,940 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:51:32,941 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:32,943 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:51:32,944 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:32,947 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:51:33,448 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:33,448 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:51:33,449 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:51:33,449 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:51:33,449 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:51:33,449 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:51:33,450 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:51:34,250 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:51:34,251 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:51:34,251 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:51:34,876 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:34,876 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:34,878 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:34,879 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:34,879 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:34,880 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:51:34,880 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:51:34,881 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:51:35,400 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 15:51:35,615 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:51:35,617 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:51:35,618 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:51:35,618 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:51:35,619 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:51:35,619 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:51:35,620 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:51:35,620 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:51:35,621 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:51:35,622 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:51:35,623 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:51:35,625 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:51:35,626 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:51:35,630 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:51:35,631 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:51:35,631 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:51:35,632 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:51:35,634 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:51:35,635 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:35,637 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:35,638 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 1
2025-07-30 15:51:35,638 - modules.friend_request_window - ERROR - 🚨 检测到单窗口环境且出现频率错误
2025-07-30 15:51:35,638 - modules.friend_request_window - ERROR - 🚨 单窗口环境下无法进行窗口切换，程序必须终止
2025-07-30 15:51:35,639 - modules.friend_request_window - ERROR - 💡 建议：启动多个微信窗口以支持自动切换功能
2025-07-30 15:51:35,641 - modules.friend_request_window - ERROR - 🚨 已设置程序终止标志，主程序将停止执行
2025-07-30 15:51:35,643 - modules.friend_request_window - ERROR - ❌ 所有频率错误处理方式都失败
2025-07-30 15:51:36,644 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:51:36,646 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 15:51:36,647 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 15:51:36,647 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 15:51:36,647 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 15:51:36,648 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:51:36,648 - modules.friend_request_window - INFO -    📝 备注信息: '014325120080-郑智冉-2025-07-30 23:51:16'
2025-07-30 15:51:37,149 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 15:51:37,150 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:37,150 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:37,151 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:51:37,152 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:37,153 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:37,153 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:37,153 - modules.wechat_auto_add_simple - INFO - ✅ 15556311090 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 15:51:37,154 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15556311090
2025-07-30 15:51:37,155 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:37,156 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:37,157 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:51:37,158 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:37,160 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:37,161 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:40,879 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2837
2025-07-30 15:51:40,880 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13593609179 (陈涛)
2025-07-30 15:51:40,880 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:40,880 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:40,881 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:51:40,882 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:40,883 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:40,883 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:47,455 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13593609179
2025-07-30 15:51:47,456 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:51:47,456 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13593609179 执行添加朋友操作...
2025-07-30 15:51:47,457 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:51:47,457 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:51:47,458 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:51:47,459 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:51:47,465 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:51:47,467 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:51:47,467 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:51:47,468 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:51:47,470 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:51:47,471 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:51:47,473 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:51:47,473 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:51:47,479 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:51:47,481 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:51:47,483 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:51:47,486 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 15:51:47,490 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:51:47,992 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:51:47,994 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:51:48,060 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差32.70, 边缘比例0.0420
2025-07-30 15:51:48,069 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_155148.png
2025-07-30 15:51:48,072 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:51:48,075 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:51:48,079 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:51:48,081 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:51:48,082 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:51:48,087 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_155148.png
2025-07-30 15:51:48,091 - WeChatAutoAdd - INFO - 底部区域原始检测到 32 个轮廓
2025-07-30 15:51:48,094 - WeChatAutoAdd - INFO - 重要轮廓: 位置(117,236), 尺寸93x31, 长宽比3.00, 已知特征:False
2025-07-30 15:51:48,096 - WeChatAutoAdd - INFO - 发现目标候选: 位置(117,236), 尺寸93x31
2025-07-30 15:51:48,098 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(306,209), 尺寸1x86, 长宽比0.01, 面积86
2025-07-30 15:51:48,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,101 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,103 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,108 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,110 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(221,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 15:51:48,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,113 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(210,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,114 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(202,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,115 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,209), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 15:51:48,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(190,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(187,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,126 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(180,209), 尺寸4x1, 长宽比4.00, 面积4
2025-07-30 15:51:48,128 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(175,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,129 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,132 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,209), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 15:51:48,133 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,135 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,209), 尺寸10x1, 长宽比10.00, 面积10
2025-07-30 15:51:48,145 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,146 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(129,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,149 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(118,209), 尺寸5x1, 长宽比5.00, 面积5
2025-07-30 15:51:48,151 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(104,209), 尺寸11x1, 长宽比11.00, 面积11
2025-07-30 15:51:48,157 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(100,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,160 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(94,209), 尺寸3x1, 长宽比3.00, 面积3
2025-07-30 15:51:48,166 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(89,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,168 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(85,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,177 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,209), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:51:48,180 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,209), 尺寸6x1, 长宽比6.00, 面积6
2025-07-30 15:51:48,182 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(23,209), 尺寸282x146, 长宽比1.93, 面积41172
2025-07-30 15:51:48,183 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,209), 尺寸2x88, 长宽比0.02, 面积176
2025-07-30 15:51:48,187 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:51:48,190 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:51:48,193 - WeChatAutoAdd - INFO - 选择目标候选按钮: Y=236, 符合'添加到通讯录'特征
2025-07-30 15:51:48,194 - WeChatAutoAdd - INFO - 在底部找到按钮: (163, 251), 尺寸: 93x31, 位置得分: 1.0, 目标候选: True, 绿色按钮: False, 特殊位置: False
2025-07-30 15:51:48,195 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 15:51:48,205 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_155148.png
2025-07-30 15:51:48,209 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 15:51:48,210 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:51:48,512 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(163, 251) -> 屏幕坐标(1363, 251)
2025-07-30 15:51:49,289 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:51:49,290 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:51:49,292 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:49,292 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:49,293 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:49,294 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:49,295 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:49,295 - modules.wechat_auto_add_simple - INFO - ✅ 13593609179 添加朋友操作执行成功
2025-07-30 15:51:49,296 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:51:49,296 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:51:49,297 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:51:49,298 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:51:49,298 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:51:49,299 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:51:51,300 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:51:51,301 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:51:51,301 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:51:51,301 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:51:51,302 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:51:51,303 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:51:51,303 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:51:51,304 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:51:51,304 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:51:51,304 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13593609179
2025-07-30 15:51:51,305 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:51:51,305 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:51:51,306 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:51:51,306 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:51:51,307 - modules.friend_request_window - INFO -    📱 phone: '13593609179'
2025-07-30 15:51:51,307 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:51:51,308 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:51:51,937 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:51:51,938 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:51:51,938 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:51:51,938 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:51:51,940 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13593609179
2025-07-30 15:51:51,940 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:51:51,941 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:51:51,941 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:51:51,941 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:51:51,942 - modules.friend_request_window - INFO -    📱 手机号码: 13593609179
2025-07-30 15:51:51,942 - modules.friend_request_window - INFO -    🆔 准考证: 014425111664
2025-07-30 15:51:51,942 - modules.friend_request_window - INFO -    👤 姓名: 陈涛
2025-07-30 15:51:51,942 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:51:51,943 - modules.friend_request_window - INFO -    📝 备注格式: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:51:51,943 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:51:51,943 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:51:51,944 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:51:51,945 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 855492, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:51:51,946 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 855492)
2025-07-30 15:51:51,946 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:51:51,947 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:51:51,947 - modules.friend_request_window - INFO - 🔄 激活窗口: 855492
2025-07-30 15:51:52,649 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:51:52,650 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:51:52,650 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:51:52,650 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:51:52,651 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:51:52,651 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:51:52,651 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:51:52,655 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:51:52,656 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:51:52,656 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:51:52,657 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:51:52,657 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:51:52,658 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:51:52,658 - modules.friend_request_window - INFO -    📝 remark参数: '014425111664-陈涛-2025-07-30 23:51:51' (类型: <class 'str'>, 长度: 35)
2025-07-30 15:51:52,658 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:51:52,658 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:51:52,659 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:51:52,659 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:51:52,659 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:51:52,659 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:51:52,660 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:51:52,660 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:51:52,660 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:51:53,606 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:51:58,848 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:51:58,848 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:51:58,849 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:51:58,849 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:51:58,850 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 15:51:59,160 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:51:59,160 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:52:00,063 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:52:00,073 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:52:00,079 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:52:00,079 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:52:00,081 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:52:00,081 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:52:00,582 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:52:00,583 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:52:00,583 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:52:00,584 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:52:00,584 - modules.friend_request_window - INFO -    📝 内容: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:52:00,585 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 15:52:00,586 - modules.friend_request_window - INFO -    🔤 内容编码: b'014425111664-\xe9\x99\x88\xe6\xb6\x9b-2025-07-30 23:51:51'
2025-07-30 15:52:00,586 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:52:01,505 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:52:06,747 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:52:06,747 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:52:06,748 - modules.friend_request_window - INFO -    📝 原始文本: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:52:06,748 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 15:52:06,749 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 15:52:07,058 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:52:07,059 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:52:07,961 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:52:07,968 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:52:07,968 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:52:07,969 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:52:07,970 - modules.friend_request_window - INFO -    📝 已填写内容: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:52:07,970 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 15:52:08,471 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:52:08,472 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:52:08,472 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:52:08,473 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:52:08,473 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:52:08,473 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:52:08,474 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:52:09,275 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:52:09,275 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:52:09,275 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:52:09,888 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:09,889 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:09,891 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:09,892 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:09,893 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:09,893 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:52:09,895 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:52:09,897 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:52:10,416 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 15:52:10,640 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:52:10,647 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:52:10,663 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:52:10,663 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:52:10,664 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:52:10,664 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:52:10,665 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:52:10,665 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:52:10,665 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:52:10,666 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:52:10,666 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:52:10,666 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:52:10,667 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:52:10,671 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:52:10,672 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:52:10,672 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:52:10,673 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:52:10,674 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:52:10,675 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:10,676 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:10,678 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 1
2025-07-30 15:52:10,678 - modules.friend_request_window - ERROR - 🚨 检测到单窗口环境且出现频率错误
2025-07-30 15:52:10,679 - modules.friend_request_window - ERROR - 🚨 单窗口环境下无法进行窗口切换，程序必须终止
2025-07-30 15:52:10,679 - modules.friend_request_window - ERROR - 💡 建议：启动多个微信窗口以支持自动切换功能
2025-07-30 15:52:10,680 - modules.friend_request_window - ERROR - 🚨 已设置程序终止标志，主程序将停止执行
2025-07-30 15:52:10,680 - modules.friend_request_window - ERROR - ❌ 所有频率错误处理方式都失败
2025-07-30 15:52:11,680 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:52:11,683 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 15:52:11,684 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 15:52:11,684 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 15:52:11,685 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 15:52:11,685 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:52:11,685 - modules.friend_request_window - INFO -    📝 备注信息: '014425111664-陈涛-2025-07-30 23:51:51'
2025-07-30 15:52:12,187 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 15:52:12,188 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:12,189 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:12,190 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:52:12,190 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:12,191 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:12,192 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:12,192 - modules.wechat_auto_add_simple - INFO - ✅ 13593609179 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 15:52:12,192 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13593609179
2025-07-30 15:52:12,194 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:12,194 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:12,195 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:52:12,196 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:12,197 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:12,197 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:13,658 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 15:52:13,659 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 15:52:13,659 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 15:52:13,660 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 15:52:13,660 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 15:52:13,661 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 15:52:13,661 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 15:52:13,661 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 15:52:13,661 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 15:52:13,661 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 15:52:13,662 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 15:52:13,662 - __main__ - INFO - � 更新全局进度：已处理 2/2835 个联系人（剩余 2835 个）
2025-07-30 15:52:13,663 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 15:52:16,664 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:52:16,664 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:52:16,666 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:52:16,666 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:52:16,669 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:52:16,669 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:52:16,671 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 15:52:16,672 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 15:52:16,675 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:52:16,675 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:52:16,677 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:52:16,678 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:52:16,679 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:52:16,679 - __main__ - INFO - 📊 窗口切换后检查: 当前微信窗口数量=1
2025-07-30 15:52:16,680 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:52:16,680 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:52:16,682 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:52:16,683 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:52:16,689 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 15:52:16,690 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:52:16,691 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 15:52:16,691 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 15:52:16,692 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:52:16,692 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:52:16,693 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:52:16,693 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:52:16,694 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:52:16,694 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 15:52:16,694 - __main__ - INFO - 📊 当前进度：已处理 2/2835 个联系人（剩余 2835 个）
2025-07-30 15:52:16,695 - __main__ - INFO - 
============================================================
2025-07-30 15:52:16,695 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 2 轮)
2025-07-30 15:52:16,696 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:52:16,697 - __main__ - INFO - 📊 全局进度：已处理 2/2835 个联系人（剩余 2835 个）
2025-07-30 15:52:16,697 - __main__ - INFO - ============================================================
2025-07-30 15:52:16,698 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:52:16,698 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:52:16,698 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:52:16,698 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:52:16,699 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2426918)
2025-07-30 15:52:16,699 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:52:17,800 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:52:17,801 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 15:52:17,801 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 15:52:18,105 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:52:18,105 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:52:18,106 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:52:18,106 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:52:18,106 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:52:18,106 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:52:18,107 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:52:18,107 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:52:18,107 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:52:18,108 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:52:18,309 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:52:18,309 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:52:18,311 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 15:52:18,612 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:52:18,612 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:52:18,612 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:52:19,613 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:52:19,613 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:52:19,614 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:52:19,614 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:52:19,614 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:52:19,615 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:52:19,615 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:52:19,616 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:52:19,616 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:52:19,818 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:52:19,818 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:52:19,818 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:52:22,204 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:52:22,205 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:52:22,205 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 15:52:24,227 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:52:24,428 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:52:24,429 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:52:24,429 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:52:26,836 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:52:26,836 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:52:26,837 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 15:52:29,622 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:52:29,823 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:52:29,823 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:52:29,824 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:52:32,202 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:52:32,203 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:52:32,203 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 15:52:34,541 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:52:34,742 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:52:34,743 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:52:34,743 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:52:37,137 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:52:37,137 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:52:37,138 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 15:52:38,713 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:52:38,914 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:52:38,915 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:52:38,916 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:52:41,319 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:52:41,320 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:52:41,320 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:52:41,320 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:52:41,321 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:52:41,322 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:52:41,322 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:52:41,323 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:52:41,325 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:52:41,325 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1642110)
2025-07-30 15:52:41,325 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1642110) - 增强版
2025-07-30 15:52:41,628 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:52:41,629 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:52:41,629 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:52:41,629 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:52:41,630 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 15:52:41,630 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:52:41,836 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:52:41,836 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:52:42,038 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:52:42,038 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:52:42,038 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:52:42,038 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:52:42,039 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:52:42,039 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:52:42,039 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:52:43,040 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:52:43,040 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:52:43,042 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:52:43,043 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:52:43,043 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:52:43,046 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:52:43,047 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:52:43,050 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:52:43,055 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:52:43,055 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:52:43,056 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 15:52:43,057 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 15:52:43,371 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:52:43,372 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:52:43,372 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:52:43,372 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:52:43,373 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:52:43,373 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:52:43,373 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:52:43,374 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:52:43,374 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:52:43,374 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:52:43,576 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:52:43,576 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:52:43,578 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 15:52:43,878 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:52:43,879 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:52:43,879 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:52:43,880 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:52:44,880 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:52:44,881 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:52:44,881 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:52:44,884 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_155244.log
2025-07-30 15:52:44,886 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:52:44,887 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:52:44,891 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:52:44,892 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 15:52:44,893 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:52:44,894 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:52:44,898 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 15:52:44,898 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:52:44,899 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 15:52:44,900 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:52:44,901 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 15:52:44,903 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:52:44,904 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:52:44,905 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1642110
2025-07-30 15:52:44,905 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1642110) - 增强版
2025-07-30 15:52:45,215 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:52:45,216 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:52:45,216 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:52:45,217 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:52:45,217 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:52:45,217 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:52:45,217 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:52:45,218 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:52:45,419 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:52:45,420 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:52:45,421 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1642110 (API返回: None)
2025-07-30 15:52:45,721 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:52:45,722 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:52:45,723 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:52:45,723 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:52:45,724 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:52:45,724 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:52:45,724 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:52:45,730 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:52:45,731 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:52:46,298 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:52:46,309 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:52:46,603 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2835 个
2025-07-30 15:52:46,604 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 15:52:46,604 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2833 个
2025-07-30 15:52:46,605 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2833 个 (总计: 3135 个)
2025-07-30 15:52:46,605 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:52:46,605 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:52:46,606 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:46,606 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:46,607 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:46,608 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:46,608 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:46,608 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:52:46,608 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2833
2025-07-30 15:52:46,609 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13040078217 (吴春龙)
2025-07-30 15:52:46,610 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:46,611 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:46,612 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:46,614 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:46,614 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:53,259 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13040078217
2025-07-30 15:52:53,260 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:52:53,260 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13040078217 执行添加朋友操作...
2025-07-30 15:52:53,260 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:52:53,261 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:52:53,262 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:52:53,263 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:52:53,267 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 15:52:53,275 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:52:53,277 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:52:53,278 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:52:53,279 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:52:53,279 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:52:53,280 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:52:53,280 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:52:53,288 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:52:53,290 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:52:53,292 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:52:53,294 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 15:52:53,296 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:52:53,799 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:52:53,800 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:52:53,869 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.84, 边缘比例0.0350
2025-07-30 15:52:53,875 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_155253.png
2025-07-30 15:52:53,878 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:52:53,879 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:52:53,880 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:52:53,882 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:52:53,886 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:52:53,890 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_155253.png
2025-07-30 15:52:53,892 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 15:52:53,894 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:52:53,895 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:52:53,896 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:52:53,900 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 15:52:53,903 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 15:52:53,904 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 15:52:53,906 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 15:52:53,907 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:52:53,908 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 15:52:53,910 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 15:52:53,912 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 15:52:53,913 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:52:53,914 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 15:52:53,920 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:52:53,921 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:52:53,923 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 15:52:53,924 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 15:52:53,927 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:52:53,928 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:52:53,929 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 15:52:53,932 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 15:52:53,936 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 15:52:53,939 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:52:53,940 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 15:52:53,945 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 15:52:53,952 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 15:52:53,954 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 15:52:53,958 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 15:52:53,972 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 15:52:53,976 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 15:52:53,981 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 15:52:53,989 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 15:52:53,991 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 15:52:53,994 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 15:52:53,996 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 15:52:54,002 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 15:52:54,004 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 15:52:54,006 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:52:54,007 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 15:52:54,008 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 15:52:54,010 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:52:54,012 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:52:54,024 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_155254.png
2025-07-30 15:52:54,028 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:52:54,030 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 15:52:54,039 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_155254.png
2025-07-30 15:52:54,064 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:52:54,070 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 15:52:54,071 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:52:54,072 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:52:54,374 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 15:52:55,150 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:52:55,152 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:52:55,153 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:55,154 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:55,155 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:55,156 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:55,156 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:55,158 - modules.wechat_auto_add_simple - INFO - ✅ 13040078217 添加朋友操作执行成功
2025-07-30 15:52:55,158 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:55,159 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:55,161 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:55,163 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:55,163 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:55,164 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:52:57,167 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:52:57,167 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:52:57,168 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:52:57,168 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:52:57,168 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:52:57,169 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:52:57,169 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:52:57,170 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:52:57,170 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:52:57,170 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13040078217
2025-07-30 15:52:57,171 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:52:57,171 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:52:57,172 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:52:57,173 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:52:57,174 - modules.friend_request_window - INFO -    📱 phone: '13040078217'
2025-07-30 15:52:57,174 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:52:57,175 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:52:57,726 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:52:57,726 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:52:57,727 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:52:57,727 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:52:57,728 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13040078217
2025-07-30 15:52:57,728 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:52:57,729 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:52:57,729 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:52:57,730 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:52:57,730 - modules.friend_request_window - INFO -    📱 手机号码: 13040078217
2025-07-30 15:52:57,730 - modules.friend_request_window - INFO -    🆔 准考证: 014425120134
2025-07-30 15:52:57,730 - modules.friend_request_window - INFO -    👤 姓名: 吴春龙
2025-07-30 15:52:57,731 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:52:57,731 - modules.friend_request_window - INFO -    📝 备注格式: '014425120134-吴春龙-2025-07-30 23:52:57'
2025-07-30 15:52:57,732 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:52:57,732 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014425120134-吴春龙-2025-07-30 23:52:57'
2025-07-30 15:52:57,733 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:52:57,735 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 15:52:57,737 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 15:52:57,738 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:57,739 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:57,740 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:57,741 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:57,742 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:52:57,742 - modules.wechat_auto_add_simple - INFO - ✅ 13040078217 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 15:52:57,742 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13040078217
2025-07-30 15:52:57,743 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:52:57,743 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:52:57,744 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:52:57,745 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:52:57,746 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:01,594 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2833
2025-07-30 15:53:01,594 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 16720419498 (施卓卓)
2025-07-30 15:53:01,595 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:01,595 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:01,596 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:01,598 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:01,599 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:08,174 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 16720419498
2025-07-30 15:53:08,174 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:53:08,175 - modules.wechat_auto_add_simple - INFO - 👥 开始为 16720419498 执行添加朋友操作...
2025-07-30 15:53:08,175 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:53:08,175 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:53:08,176 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:53:08,177 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:53:08,183 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:53:08,185 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:53:08,185 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:53:08,186 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:53:08,186 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:53:08,187 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:53:08,188 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:53:08,188 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:53:08,194 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:53:08,196 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:53:08,198 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:53:08,204 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 15:53:08,207 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:53:08,711 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:53:08,712 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:53:08,783 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.92, 边缘比例0.0349
2025-07-30 15:53:08,791 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_155308.png
2025-07-30 15:53:08,794 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:53:08,802 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:53:08,804 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:53:08,805 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:53:08,808 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:53:08,812 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_155308.png
2025-07-30 15:53:08,819 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 15:53:08,821 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:53:08,822 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:53:08,824 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:53:08,826 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 15:53:08,827 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 15:53:08,829 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 15:53:08,836 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 15:53:08,838 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:53:08,840 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 15:53:08,845 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 15:53:08,854 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 15:53:08,856 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:53:08,861 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 15:53:08,864 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 15:53:08,875 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:53:08,877 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 15:53:08,880 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 15:53:08,888 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:53:08,889 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:53:08,891 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 15:53:08,893 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 15:53:08,896 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 15:53:08,902 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 15:53:08,904 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 15:53:08,906 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 15:53:08,907 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 15:53:08,909 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 15:53:08,912 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 15:53:08,917 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 15:53:08,921 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 15:53:08,922 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 15:53:08,924 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 15:53:08,926 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 15:53:08,928 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 15:53:08,929 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 15:53:08,937 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 15:53:08,938 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 15:53:08,940 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:53:08,943 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 15:53:08,944 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 15:53:08,945 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:53:08,949 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:53:08,958 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_155308.png
2025-07-30 15:53:08,961 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:53:08,963 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 15:53:08,973 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_155308.png
2025-07-30 15:53:09,000 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:53:09,006 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 15:53:09,009 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:53:09,011 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:53:09,313 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 15:53:10,101 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:53:10,105 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:53:10,110 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:10,111 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:10,113 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:10,118 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:10,119 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:10,119 - modules.wechat_auto_add_simple - INFO - ✅ 16720419498 添加朋友操作执行成功
2025-07-30 15:53:10,119 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:10,120 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:10,121 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:10,123 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:10,125 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:10,125 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:53:12,127 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:53:12,128 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:53:12,128 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:53:12,128 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:53:12,129 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:53:12,129 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:53:12,129 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:53:12,129 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:53:12,131 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:53:12,131 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 16720419498
2025-07-30 15:53:12,132 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:53:12,132 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:53:12,132 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:53:12,133 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:53:12,134 - modules.friend_request_window - INFO -    📱 phone: '16720419498'
2025-07-30 15:53:12,135 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:53:12,135 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:53:12,710 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:53:12,711 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:53:12,711 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:53:12,711 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:53:12,713 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 16720419498
2025-07-30 15:53:12,713 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:53:12,714 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:53:12,714 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:53:12,714 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:53:12,715 - modules.friend_request_window - INFO -    📱 手机号码: 16720419498
2025-07-30 15:53:12,715 - modules.friend_request_window - INFO -    🆔 准考证: 014425111676
2025-07-30 15:53:12,715 - modules.friend_request_window - INFO -    👤 姓名: 施卓卓
2025-07-30 15:53:12,715 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:53:12,716 - modules.friend_request_window - INFO -    📝 备注格式: '014425111676-施卓卓-2025-07-30 23:53:12'
2025-07-30 15:53:12,716 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:53:12,717 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014425111676-施卓卓-2025-07-30 23:53:12'
2025-07-30 15:53:12,717 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:53:12,720 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 15:53:12,720 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 15:53:12,723 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:12,723 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:12,724 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:12,725 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:12,726 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:12,726 - modules.wechat_auto_add_simple - INFO - ✅ 16720419498 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 15:53:12,726 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 16720419498
2025-07-30 15:53:12,727 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:12,728 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:12,729 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:12,730 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:12,730 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:14,238 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 15:53:14,239 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 15:53:14,239 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 15:53:14,240 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 15:53:14,241 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 15:53:14,241 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 15:53:14,241 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 15:53:14,241 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 15:53:14,242 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 15:53:14,242 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 15:53:14,242 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 15:53:14,243 - __main__ - INFO - � 更新全局进度：已处理 4/2833 个联系人（剩余 2833 个）
2025-07-30 15:53:14,243 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 15:53:17,244 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:53:17,244 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:53:17,245 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:53:17,246 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:53:17,247 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:53:17,249 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:53:17,250 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:53:17,253 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 15:53:17,255 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 15:53:17,255 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:53:17,255 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:53:17,257 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:53:17,259 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:53:17,259 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:53:17,260 - __main__ - INFO -   🔥 窗口 2: 添加朋友 (句柄: 1642110)
2025-07-30 15:53:17,260 - __main__ - INFO - 📊 窗口切换后检查: 当前微信窗口数量=2
2025-07-30 15:53:17,261 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:53:17,262 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:53:17,264 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:53:17,269 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:53:17,269 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:53:17,271 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:53:17,272 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 15:53:17,274 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 15:53:17,274 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 15:53:17,275 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 15:53:17,275 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 15:53:17,276 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 15:53:17,276 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 15:53:17,277 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 15:53:17,277 - __main__ - INFO -   🔥 窗口 2: 添加朋友 (句柄: 1642110)
2025-07-30 15:53:17,277 - __main__ - INFO - 🔄 完成第 2 轮窗口循环，重新开始下一轮
2025-07-30 15:53:17,277 - __main__ - INFO - 📊 当前进度：已处理 4/2833 个联系人（剩余 2833 个）
2025-07-30 15:53:17,278 - __main__ - INFO - 
============================================================
2025-07-30 15:53:17,278 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 3 轮)
2025-07-30 15:53:17,278 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:53:17,279 - __main__ - INFO - 📊 全局进度：已处理 4/2833 个联系人（剩余 2833 个）
2025-07-30 15:53:17,282 - __main__ - INFO - ============================================================
2025-07-30 15:53:17,286 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 15:53:17,287 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 15:53:17,287 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 15:53:17,287 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 15:53:17,288 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2426918)
2025-07-30 15:53:17,288 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 15:53:18,389 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 15:53:18,390 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 15:53:18,390 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 15:53:18,694 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:53:18,694 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:53:18,694 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:53:18,695 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:53:18,695 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:53:18,695 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:53:18,696 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:53:18,696 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:53:18,697 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:53:18,697 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:53:18,900 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:53:18,900 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:53:18,902 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 15:53:19,203 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:53:19,204 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 15:53:19,205 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 15:53:20,206 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 15:53:20,206 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 15:53:20,207 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 15:53:20,207 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 15:53:20,207 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 15:53:20,208 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 15:53:20,208 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 15:53:20,208 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 15:53:20,209 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 15:53:20,409 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 15:53:20,410 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 15:53:20,410 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 15:53:22,783 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 15:53:22,784 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 15:53:22,784 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 15:53:25,448 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 15:53:25,650 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 15:53:25,655 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 15:53:25,656 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 15:53:28,049 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 15:53:28,050 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 15:53:28,051 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 15:53:30,308 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 15:53:30,508 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 15:53:30,509 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 15:53:30,509 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 15:53:32,883 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 15:53:32,884 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 15:53:32,884 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 15:53:34,786 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 15:53:34,987 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 15:53:34,987 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 15:53:34,988 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 15:53:37,365 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 15:53:37,365 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 15:53:37,366 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 15:53:39,351 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 15:53:39,552 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 15:53:39,552 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 15:53:39,553 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 15:53:41,965 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 15:53:41,966 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 15:53:41,966 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:53:41,966 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 15:53:41,967 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:53:41,968 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:53:41,969 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:53:41,969 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:53:41,971 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:53:41,972 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1642110)
2025-07-30 15:53:41,972 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1642110) - 增强版
2025-07-30 15:53:42,276 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:53:42,276 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:53:42,277 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:53:42,277 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:53:42,278 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 15:53:42,278 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:53:42,483 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 15:53:42,483 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:53:42,685 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:53:42,685 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:53:42,685 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 15:53:42,686 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 15:53:42,686 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 15:53:42,686 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 15:53:42,687 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 15:53:43,687 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 15:53:43,688 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:53:43,689 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1642110, 进程: Weixin.exe)
2025-07-30 15:53:43,690 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 15:53:43,690 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 15:53:43,693 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 15:53:43,694 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 15:53:43,695 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 15:53:43,698 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 15:53:43,699 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 15:53:43,701 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 15:53:43,702 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 15:53:44,015 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:53:44,015 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:53:44,016 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 15:53:44,016 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 15:53:44,017 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 15:53:44,017 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 15:53:44,018 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 15:53:44,018 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 15:53:44,019 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 15:53:44,019 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 15:53:44,221 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:53:44,222 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:53:44,223 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 15:53:44,524 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:53:44,525 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 15:53:44,525 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 15:53:44,525 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 15:53:45,526 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 15:53:45,526 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 15:53:45,527 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 15:53:45,530 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_155345.log
2025-07-30 15:53:45,531 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:53:45,531 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 15:53:45,531 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 15:53:45,532 - __main__ - INFO - 🔄 传递全局联系人索引: 4
2025-07-30 15:53:45,533 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 15:53:45,533 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 15:53:45,536 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 15:53:45,536 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 15:53:45,537 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 15:53:45,538 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 15:53:45,539 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 15:53:45,539 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 15:53:45,540 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:53:45,540 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1642110
2025-07-30 15:53:45,541 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1642110) - 增强版
2025-07-30 15:53:45,851 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 15:53:45,852 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 15:53:45,852 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 15:53:45,853 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 15:53:45,853 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 15:53:45,853 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 15:53:45,854 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 15:53:45,854 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 15:53:46,056 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 15:53:46,056 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 15:53:46,058 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1642110 (API返回: None)
2025-07-30 15:53:46,359 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 15:53:46,359 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 15:53:46,360 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 15:53:46,360 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 15:53:46,361 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 15:53:46,361 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 15:53:46,362 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 15:53:46,366 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 15:53:46,368 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 15:53:47,030 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 15:53:47,031 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:53:47,342 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2833 个
2025-07-30 15:53:47,343 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 5 个联系人开始处理
2025-07-30 15:53:47,343 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2829 个
2025-07-30 15:53:47,343 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2829 个 (总计: 3135 个)
2025-07-30 15:53:47,344 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 15:53:47,344 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 15:53:47,345 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:47,346 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:47,346 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:47,347 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:47,348 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:47,349 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 15:53:47,349 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2829
2025-07-30 15:53:47,350 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18082163365 (杨一乐)
2025-07-30 15:53:47,350 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:47,351 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:47,353 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:47,355 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:47,356 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:53,905 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18082163365
2025-07-30 15:53:53,905 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:53:53,905 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18082163365 执行添加朋友操作...
2025-07-30 15:53:53,906 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:53:53,906 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:53:53,907 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:53:53,908 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:53:53,913 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:53:53,917 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:53:53,918 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:53:53,918 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:53:53,919 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:53:53,919 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:53:53,920 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:53:53,920 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:53:53,928 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 15:53:53,935 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 15:53:53,938 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:53:53,942 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 15:53:53,945 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 15:53:54,461 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 15:53:54,491 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 15:53:54,567 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.30, 边缘比例0.0393
2025-07-30 15:53:54,583 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_155354.png
2025-07-30 15:53:54,592 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 15:53:54,594 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 15:53:54,599 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 15:53:54,601 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 15:53:54,605 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 15:53:54,615 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_155354.png
2025-07-30 15:53:54,618 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 15:53:54,620 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 15:53:54,623 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 15:53:54,625 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 15:53:54,626 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 15:53:54,633 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 15:53:54,635 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 15:53:54,638 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 15:53:54,649 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_155354.png
2025-07-30 15:53:54,652 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 15:53:54,653 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 15:53:54,657 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_155354.png
2025-07-30 15:53:54,685 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 15:53:54,688 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 15:53:54,690 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 15:53:54,692 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 15:53:54,994 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 15:53:55,781 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 15:53:55,783 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 15:53:55,786 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:55,786 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:55,787 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:55,788 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:55,789 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:55,789 - modules.wechat_auto_add_simple - INFO - ✅ 18082163365 添加朋友操作执行成功
2025-07-30 15:53:55,790 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:53:55,791 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:53:55,792 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:53:55,793 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:53:55,796 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:53:55,798 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 15:53:57,799 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 15:53:57,800 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 15:53:57,800 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 15:53:57,800 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 15:53:57,801 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 15:53:57,801 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 15:53:57,801 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 15:53:57,801 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 15:53:57,802 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 15:53:57,802 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18082163365
2025-07-30 15:53:57,802 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 15:53:57,803 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:53:57,803 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:53:57,803 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 15:53:57,804 - modules.friend_request_window - INFO -    📱 phone: '18082163365'
2025-07-30 15:53:57,804 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 15:53:57,804 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 15:53:58,376 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 15:53:58,376 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 15:53:58,377 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 15:53:58,377 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 15:53:58,379 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18082163365
2025-07-30 15:53:58,379 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 15:53:58,380 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:53:58,380 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 15:53:58,380 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 15:53:58,380 - modules.friend_request_window - INFO -    📱 手机号码: 18082163365
2025-07-30 15:53:58,381 - modules.friend_request_window - INFO -    🆔 准考证: 014225120050
2025-07-30 15:53:58,381 - modules.friend_request_window - INFO -    👤 姓名: 杨一乐
2025-07-30 15:53:58,381 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:53:58,382 - modules.friend_request_window - INFO -    📝 备注格式: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:53:58,383 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 15:53:58,383 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:53:58,384 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:53:58,386 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 1117636, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 15:53:58,388 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 1117636)
2025-07-30 15:53:58,389 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 15:53:58,390 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 15:53:58,390 - modules.friend_request_window - INFO - 🔄 激活窗口: 1117636
2025-07-30 15:53:59,093 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 15:53:59,093 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 15:53:59,094 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 15:53:59,094 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 15:53:59,095 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 15:53:59,095 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 15:53:59,095 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 15:53:59,095 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 15:53:59,096 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 15:53:59,096 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 15:53:59,096 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 15:53:59,096 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 15:53:59,097 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 15:53:59,097 - modules.friend_request_window - INFO -    📝 remark参数: '014225120050-杨一乐-2025-07-30 23:53:58' (类型: <class 'str'>, 长度: 36)
2025-07-30 15:53:59,097 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 15:53:59,097 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:53:59,098 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 15:53:59,099 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 15:53:59,099 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 15:53:59,100 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 15:53:59,101 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 15:53:59,102 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 15:53:59,103 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 15:54:00,018 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 15:54:05,260 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 15:54:05,262 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 15:54:05,262 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 15:54:05,263 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 15:54:05,264 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 15:54:05,575 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:54:05,575 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:54:06,478 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:54:06,488 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:54:06,489 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 15:54:06,490 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 15:54:06,491 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 15:54:06,491 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 15:54:06,992 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 15:54:06,992 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 15:54:06,993 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 15:54:06,993 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 15:54:06,994 - modules.friend_request_window - INFO -    📝 内容: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:54:06,994 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 15:54:06,995 - modules.friend_request_window - INFO -    🔤 内容编码: b'014225120050-\xe6\x9d\xa8\xe4\xb8\x80\xe4\xb9\x90-2025-07-30 23:53:58'
2025-07-30 15:54:06,995 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 15:54:07,931 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 15:54:13,237 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 15:54:13,238 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 15:54:13,238 - modules.friend_request_window - INFO -    📝 原始文本: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:54:13,238 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 15:54:13,239 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: ' 240x82...' (前50字符)
2025-07-30 15:54:13,549 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 15:54:13,549 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 15:54:14,452 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 15:54:14,460 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 15:54:14,461 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:54:14,462 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 15:54:14,463 - modules.friend_request_window - INFO -    📝 已填写内容: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:54:14,463 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 15:54:14,964 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:54:14,965 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 15:54:14,965 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 15:54:14,965 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 15:54:14,965 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 15:54:14,966 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 15:54:14,966 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 15:54:15,767 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 15:54:15,767 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 15:54:15,767 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 15:54:16,379 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:54:16,380 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:54:16,381 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:54:16,383 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:54:16,383 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:54:16,383 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 15:54:16,383 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 15:54:16,384 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 15:54:16,885 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 15:54:16,887 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 15:54:16,887 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 15:54:16,888 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 15:54:16,888 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 15:54:16,888 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 15:54:16,889 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 15:54:16,889 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 15:54:16,889 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 15:54:16,890 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:54:16,890 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 15:54:16,890 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 15:54:16,891 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 15:54:16,891 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 15:54:16,892 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 15:54:16,892 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 15:54:16,893 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 15:54:16,896 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:54:16,897 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:54:16,899 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:54:16,899 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 1
2025-07-30 15:54:16,900 - modules.friend_request_window - ERROR - 🚨 检测到单窗口环境且出现频率错误
2025-07-30 15:54:16,900 - modules.friend_request_window - ERROR - 🚨 单窗口环境下无法进行窗口切换，程序必须终止
2025-07-30 15:54:16,900 - modules.friend_request_window - ERROR - 💡 建议：启动多个微信窗口以支持自动切换功能
2025-07-30 15:54:16,901 - modules.friend_request_window - ERROR - 🚨 已设置程序终止标志，主程序将停止执行
2025-07-30 15:54:16,901 - modules.friend_request_window - ERROR - ❌ 所有频率错误处理方式都失败
2025-07-30 15:54:17,902 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 15:54:17,904 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 15:54:17,904 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 15:54:17,905 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 15:54:17,905 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 15:54:17,905 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 15:54:17,905 - modules.friend_request_window - INFO -    📝 备注信息: '014225120050-杨一乐-2025-07-30 23:53:58'
2025-07-30 15:54:18,406 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 15:54:18,407 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:54:18,407 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:54:18,408 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:54:18,408 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:54:18,412 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:54:18,414 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:54:18,415 - modules.wechat_auto_add_simple - INFO - ✅ 18082163365 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 15:54:18,416 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18082163365
2025-07-30 15:54:18,418 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:54:18,418 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:54:18,420 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:54:18,422 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:54:18,423 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:54:18,423 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:54:22,388 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2829
2025-07-30 15:54:22,389 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13157153110 (房剑飞)
2025-07-30 15:54:22,389 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:54:22,389 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:54:22,390 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 15:54:22,391 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:54:22,392 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 15:54:22,393 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 15:54:29,006 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13157153110
2025-07-30 15:54:29,007 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 15:54:29,007 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13157153110 执行添加朋友操作...
2025-07-30 15:54:29,008 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 15:54:29,008 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 15:54:29,011 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 15:54:29,015 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 15:54:29,020 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 15:54:29,024 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 15:54:29,024 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 15:54:29,024 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 15:54:29,024 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 15:54:29,025 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 15:54:29,025 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 15:54:29,025 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 15:54:29,034 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 15:54:29,040 - WeChatAutoAdd - INFO - 共找到 1 个微信窗口
2025-07-30 15:54:29,048 - WeChatAutoAdd - WARNING - 未找到微信添加朋友窗口
2025-07-30 15:54:29,052 - WeChatAutoAdd - ERROR - 未找到微信添加朋友窗口
2025-07-30 15:54:29,055 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:54:29,055 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:54:29,056 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:54:29,057 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 0 个
2025-07-30 15:54:29,057 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-30 15:54:29,057 - modules.wechat_auto_add_simple - ERROR - ❌ 13157153110 添加朋友操作执行失败
2025-07-30 15:54:29,058 - modules.wechat_auto_add_simple - WARNING - ⚠️ 添加朋友失败: 13157153110 - 添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮
2025-07-30 15:54:29,061 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 15:54:29,063 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 15:54:29,065 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 15:54:29,066 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 0 个
2025-07-30 15:54:29,066 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 0 个微信窗口
2025-07-30 15:54:30,504 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 15:54:30,504 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 15:54:30,504 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 15:54:30,506 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 15:54:30,506 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 15:54:30,506 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 15:54:30,507 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 15:54:30,507 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 15:54:30,507 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 15:54:30,507 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 15:54:30,507 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 15:54:30,508 - __main__ - INFO - � 更新全局进度：已处理 6/2831 个联系人（剩余 2831 个）
2025-07-30 15:54:30,510 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 15:54:33,511 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 15:54:33,511 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 15:54:33,514 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-30 15:54:33,515 - __main__ - ERROR - ❌ 未找到任何微信窗口
2025-07-30 15:54:33,515 - __main__ - INFO - 📊 窗口切换后检查: 当前微信窗口数量=0
2025-07-30 15:54:33,515 - __main__ - ERROR - 🚨 窗口切换后检测到没有微信窗口
2025-07-30 15:54:33,515 - __main__ - ERROR - 🚨 所有微信窗口都已关闭，程序立即终止
2025-07-30 15:54:33,516 - __main__ - ERROR - 📊 最终统计：已处理 6/2837 个联系人
