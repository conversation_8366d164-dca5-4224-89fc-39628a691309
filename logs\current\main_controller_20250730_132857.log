2025-07-30 13:28:57,034 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:28:57,034 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 2 个窗口
2025-07-30 13:28:57,035 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:28:57,035 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:28:57,035 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 13:28:57,036 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 13:28:57,037 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 13:28:57,038 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 13:28:57,039 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 13:28:57,039 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 13:28:57,040 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 13:28:57,041 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 13:28:57,044 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:28:57,048 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_132857.log
2025-07-30 13:28:57,053 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:28:57,053 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 13:28:57,054 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 13:28:57,054 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 13:28:57,054 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 13:28:57,055 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 13:28:57,055 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 21:28:57
2025-07-30 13:28:57,056 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 13:28:57,056 - __main__ - INFO - 📅 启动时间: 2025-07-30 21:28:57
2025-07-30 13:28:57,057 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 13:28:57,057 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:28:57,589 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:28:57,590 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:28:58,112 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:28:58,113 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:28:58,113 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 13:28:58,114 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 13:28:58,114 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 13:28:58,115 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 13:28:58,115 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 13:28:58,116 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 13:28:58,116 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 13:28:58,117 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 13:28:58,117 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 1246274)
2025-07-30 13:28:58,117 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 2032818)
2025-07-30 13:28:58,118 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 13:28:58,120 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 13:28:58,121 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 1246274)
2025-07-30 13:28:58,124 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 13:28:58,125 - __main__ - WARNING - ⚠️ 窗口 1 移动失败
2025-07-30 13:28:58,426 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 2032818)
2025-07-30 13:28:58,429 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 13:28:58,429 - __main__ - WARNING - ⚠️ 窗口 2 移动失败
2025-07-30 13:28:58,429 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 13:28:58,430 - __main__ - INFO -   ✅ 成功移动: 0 个窗口
2025-07-30 13:28:58,430 - __main__ - INFO -   ❌ 移动失败: 2 个窗口
2025-07-30 13:28:58,430 - __main__ - INFO -   📈 成功率: 0.0%
2025-07-30 13:28:58,431 - __main__ - ERROR - ❌ 所有窗口移动都失败了
2025-07-30 13:28:58,432 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 13:28:59,078 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 13:28:59,079 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 13:28:59,079 - __main__ - INFO - 📋 待处理联系人数: 2891
2025-07-30 13:28:59,082 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 13:28:59,083 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2891
2025-07-30 13:28:59,083 - __main__ - INFO - 
============================================================
2025-07-30 13:28:59,084 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 13:28:59,084 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1246274)
2025-07-30 13:28:59,085 - __main__ - INFO - 📊 全局进度：已处理 0/2891 个联系人
2025-07-30 13:28:59,085 - __main__ - INFO - ============================================================
2025-07-30 13:28:59,086 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 13:28:59,086 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1246274)
2025-07-30 13:28:59,086 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 13:28:59,086 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 13:28:59,087 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 1246274)
2025-07-30 13:28:59,088 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-07-30 13:28:59,091 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 13:29:00,092 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 13:29:01,194 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 13:29:01,194 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1246274
2025-07-30 13:29:01,195 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1246274) - 增强版
2025-07-30 13:29:01,500 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:29:01,500 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:29:01,501 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:29:01,501 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:29:01,501 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:29:01,502 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:29:01,502 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:29:01,503 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:29:01,503 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:29:01,503 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:29:01,705 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:29:01,706 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:29:01,710 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1246274 (API返回: None)
2025-07-30 13:29:02,011 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:29:02,011 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 13:29:02,011 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 13:29:03,012 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 13:29:03,012 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 13:29:03,013 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 13:29:03,013 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:29:03,014 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:29:03,015 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:29:03,015 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:29:03,016 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:29:03,016 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:29:03,216 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 13:29:03,217 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:29:03,218 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:29:05,602 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:29:05,603 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:29:05,603 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 13:29:07,280 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:29:07,480 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 13:29:07,481 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:29:07,481 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:29:09,849 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:29:09,849 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:29:09,850 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 13:29:12,638 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 13:29:12,839 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 13:29:12,839 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 13:29:12,840 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 13:29:15,217 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 13:29:15,218 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 13:29:15,218 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 13:29:16,919 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 13:29:17,120 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 13:29:17,121 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 13:29:17,121 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 13:29:19,489 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 13:29:19,489 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 13:29:19,489 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 13:29:21,531 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 13:29:21,732 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 13:29:21,732 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 13:29:21,732 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 13:29:24,117 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 13:29:24,118 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 13:29:24,118 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:29:24,118 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:29:24,118 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:29:24,120 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:29:24,121 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:29:24,121 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5963796, 进程: Weixin.exe)
2025-07-30 13:29:24,122 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:29:24,123 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:29:24,126 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 13:29:24,126 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 5963796)
2025-07-30 13:29:24,127 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5963796) - 增强版
2025-07-30 13:29:24,430 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:29:24,431 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:29:24,431 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:29:24,432 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:29:24,432 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 13:29:24,432 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:29:24,635 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 13:29:24,636 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:29:24,838 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:29:24,838 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:29:24,838 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 13:29:24,839 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 13:29:24,839 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 13:29:24,839 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 13:29:24,840 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 13:29:25,840 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 13:29:25,841 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:29:25,842 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:29:25,843 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:29:25,844 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 5963796, 进程: Weixin.exe)
2025-07-30 13:29:25,845 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:29:25,845 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:29:25,849 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 13:29:25,850 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 13:29:25,850 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 13:29:25,850 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 13:29:25,851 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 13:29:25,851 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1246274
2025-07-30 13:29:25,851 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1246274) - 增强版
2025-07-30 13:29:26,158 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:29:26,158 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:29:26,159 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:29:26,160 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:29:26,160 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:29:26,161 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:29:26,161 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:29:26,161 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:29:26,162 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:29:26,162 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:29:26,364 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:29:26,364 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:29:26,366 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1246274 (API返回: None)
2025-07-30 13:29:26,667 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:29:26,667 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 13:29:26,667 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 13:29:26,668 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 13:29:27,668 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 13:29:27,669 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 13:29:27,669 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 13:29:27,672 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_132927.log
2025-07-30 13:29:27,673 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:29:27,674 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 13:29:27,674 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 13:29:27,675 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 13:29:27,675 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 13:29:27,675 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 13:29:27,677 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 13:29:27,678 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 13:29:27,678 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 13:29:27,678 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 13:29:27,678 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 13:29:27,679 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 13:29:27,680 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 13:29:27,681 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:29:27,682 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 5963796
2025-07-30 13:29:27,683 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 5963796) - 增强版
2025-07-30 13:29:27,992 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:29:27,992 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:29:27,993 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:29:27,994 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:29:27,994 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 13:29:27,994 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:29:27,995 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 13:29:27,996 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:29:28,198 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:29:28,199 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:29:28,202 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 5963796 (API返回: None)
2025-07-30 13:29:28,503 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:29:28,503 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 13:29:28,503 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 13:29:28,504 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 13:29:28,505 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:29:28,505 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 13:29:28,505 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 13:29:28,510 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 13:29:28,511 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 13:29:28,811 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 13:29:28,812 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:29:29,063 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2891 个
2025-07-30 13:29:29,064 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2891 个 (总计: 3135 个)
2025-07-30 13:29:29,065 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 13:29:29,065 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 13:29:29,066 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:29:29,066 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 2 个窗口
2025-07-30 13:29:29,066 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 13:29:29,067 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2891
2025-07-30 13:29:29,067 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18501342852 (张鹏飞)
2025-07-30 13:29:29,068 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:29:29,068 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 2 个窗口
2025-07-30 13:29:35,642 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18501342852
2025-07-30 13:29:35,643 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 13:29:35,643 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18501342852 执行添加朋友操作...
2025-07-30 13:29:35,643 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 13:29:35,643 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 13:29:35,644 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 13:29:35,645 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 13:29:35,650 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 13:29:35,651 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 13:29:35,652 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 13:29:35,652 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 13:29:35,653 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 13:29:35,653 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 13:29:35,654 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 13:29:35,654 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 13:29:35,658 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:29:35,665 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:29:35,668 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:29:35,670 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 13:29:35,671 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 13:29:35,677 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 13:29:36,180 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 13:29:36,181 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 13:29:36,254 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差36.47, 边缘比例0.0368
2025-07-30 13:29:36,261 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_132936.png
2025-07-30 13:29:36,263 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 13:29:36,265 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 13:29:36,266 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 13:29:36,266 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 13:29:36,267 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 13:29:36,273 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_132936.png
2025-07-30 13:29:36,274 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 13:29:36,275 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 13:29:36,277 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 13:29:36,278 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 13:29:36,280 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 13:29:36,281 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 13:29:36,284 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 13:29:36,286 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 13:29:36,295 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_132936.png
2025-07-30 13:29:36,298 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 13:29:36,306 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 13:29:36,312 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_132936.png
2025-07-30 13:29:36,377 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 13:29:36,380 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 13:29:36,382 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 13:29:36,383 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 13:29:36,685 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 13:29:37,453 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 13:29:37,454 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 13:29:37,455 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:29:37,455 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 2 个窗口
2025-07-30 13:29:37,455 - modules.wechat_auto_add_simple - INFO - ✅ 18501342852 添加朋友操作执行成功
2025-07-30 13:29:37,456 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:29:37,457 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 2 个窗口
2025-07-30 13:29:37,458 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 13:29:39,459 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 13:29:39,459 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 13:29:39,460 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 13:29:39,461 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 13:29:39,461 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 13:29:39,462 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 13:29:39,462 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 13:29:39,463 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 13:29:39,463 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 13:29:39,464 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18501342852
2025-07-30 13:29:39,470 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 13:29:39,471 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:29:39,473 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:29:39,474 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 13:29:39,476 - modules.friend_request_window - INFO -    📱 phone: '18501342852'
2025-07-30 13:29:39,478 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 13:29:39,479 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 13:29:39,869 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 13:29:39,870 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 13:29:39,870 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 13:29:39,871 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:29:39,872 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18501342852
2025-07-30 13:29:39,872 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 13:29:39,873 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:29:39,874 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 13:29:39,874 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 13:29:39,875 - modules.friend_request_window - INFO -    📱 手机号码: 18501342852
2025-07-30 13:29:39,875 - modules.friend_request_window - INFO -    🆔 准考证: 014325110233
2025-07-30 13:29:39,877 - modules.friend_request_window - INFO -    👤 姓名: 张鹏飞
2025-07-30 13:29:39,878 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:29:39,879 - modules.friend_request_window - INFO -    📝 备注格式: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:39,881 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:29:39,882 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:39,883 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:29:39,889 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 5572060, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 13:29:39,892 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 5572060)
2025-07-30 13:29:39,892 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 13:29:39,893 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 13:29:39,894 - modules.friend_request_window - INFO - 🔄 激活窗口: 5572060
2025-07-30 13:29:40,598 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 13:29:40,598 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 13:29:40,599 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 13:29:40,599 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 13:29:40,600 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 13:29:40,600 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:29:40,600 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 13:29:40,600 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:29:40,601 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 13:29:40,601 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 13:29:40,601 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 13:29:40,601 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 13:29:40,602 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 13:29:40,602 - modules.friend_request_window - INFO -    📝 remark参数: '014325110233-张鹏飞-2025-07-30 21:29:39' (类型: <class 'str'>, 长度: 36)
2025-07-30 13:29:40,602 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 13:29:40,603 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:40,603 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 13:29:40,603 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 13:29:40,603 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 13:29:40,604 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 13:29:40,604 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 13:29:40,604 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 13:29:40,605 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 13:29:41,516 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 13:29:46,827 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 13:29:46,827 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 13:29:46,828 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 13:29:46,828 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 13:29:46,830 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:29:47,143 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:29:47,143 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:29:48,046 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:29:48,055 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:29:48,055 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 13:29:48,058 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 13:29:48,058 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 13:29:48,059 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 13:29:48,559 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 13:29:48,559 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 13:29:48,560 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 13:29:48,560 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 13:29:48,560 - modules.friend_request_window - INFO -    📝 内容: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:48,560 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 13:29:48,561 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110233-\xe5\xbc\xa0\xe9\xb9\x8f\xe9\xa3\x9e-2025-07-30 21:29:39'
2025-07-30 13:29:48,561 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 13:29:49,482 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 13:29:54,722 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 13:29:54,723 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 13:29:54,723 - modules.friend_request_window - INFO -    📝 原始文本: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:54,724 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 13:29:54,724 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:29:55,034 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:29:55,035 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:29:55,937 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:29:55,948 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:29:55,948 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:55,949 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 13:29:55,949 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:55,950 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 13:29:56,450 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:29:56,451 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 13:29:56,451 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 13:29:56,451 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 13:29:56,451 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 13:29:56,452 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 13:29:56,452 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 13:29:57,252 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 13:29:57,253 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 13:29:57,253 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 13:29:57,865 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:29:57,866 - modules.friend_request_window - INFO - 📋 当前黑名单中有 2 个窗口
2025-07-30 13:29:57,866 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 13:29:57,866 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 13:29:57,867 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 13:29:58,369 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 13:29:58,373 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 13:29:58,374 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 13:29:58,374 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 13:29:58,374 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 13:29:58,375 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 13:29:58,377 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 13:29:58,377 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 13:29:58,378 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 13:29:58,379 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:29:58,382 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 13:29:58,382 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 13:29:58,383 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 13:29:58,383 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 13:29:58,384 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 13:29:58,384 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 13:29:58,384 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:29:58,385 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 13:29:58,388 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 13:29:58,389 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 13:29:58,389 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 13:29:58,892 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 13:29:58,893 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 13:29:58,893 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 13:29:58,893 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 13:29:58,894 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 13:29:58,894 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 13:29:58,894 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 13:29:58,895 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 13:29:59,815 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 13:29:59,816 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 13:29:59,816 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 13:29:59,816 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 13:29:59,834 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 13:29:59,834 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 13:30:00,635 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 13:30:00,635 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 13:30:00,636 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 13:30:00,636 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 13:30:00,636 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 13:30:00,637 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 13:30:00,637 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 13:30:00,637 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 13:30:02,138 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 13:30:02,139 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 13:30:02,139 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 13:30:02,139 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 13:30:04,139 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 13:30:04,141 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:30:04,141 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 13:30:04,141 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:30:04,143 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:30:04,143 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:30:04,145 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 13:30:04,146 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 13:30:04,147 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 13:30:04,168 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 13:30:04,168 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 13:30:04,169 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 13:30:04,169 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 1246274
2025-07-30 13:30:04,170 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:30:04,171 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1246274) - 增强版
2025-07-30 13:30:04,171 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 13:30:04,693 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:30:04,693 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:30:04,694 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:30:04,694 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:30:04,694 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:30:04,695 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:30:04,695 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:30:04,695 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:30:04,696 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:30:04,696 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:30:04,897 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:30:04,898 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:30:04,898 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 13:30:04,898 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 13:30:04,899 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 13:30:04,899 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 13:30:04,899 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 13:30:06,900 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 13:30:06,901 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:30:06,901 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 13:30:06,902 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 13:30:06,902 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 13:30:06,902 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 13:30:06,902 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:30:06,903 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:30:06,903 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:30:06,903 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:30:06,904 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:30:06,904 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:30:07,112 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 13:30:07,126 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:30:07,132 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:30:09,531 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:30:09,531 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:30:09,532 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 13:30:11,425 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:30:11,626 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 13:30:11,626 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:30:11,627 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:30:13,995 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:30:13,996 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:30:13,996 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 13:30:16,004 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 13:30:16,205 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 13:30:16,205 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 13:30:16,205 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 13:30:18,579 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 13:30:18,580 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 13:30:18,580 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 13:30:21,077 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 13:30:21,278 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 13:30:21,278 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 13:30:21,279 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 13:30:23,648 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 13:30:23,649 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 13:30:23,649 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 13:30:26,183 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 13:30:26,384 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 13:30:26,384 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 13:30:26,385 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 13:30:28,763 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 13:30:28,763 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 13:30:28,763 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:30:28,764 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:30:28,764 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:30:28,765 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:30:28,766 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:30:28,766 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:30:28,767 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4328332, 进程: Weixin.exe)
2025-07-30 13:30:28,768 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:30:28,769 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:30:28,771 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 13:30:28,772 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 4328332)
2025-07-30 13:30:28,774 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4328332) - 增强版
2025-07-30 13:30:29,080 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:30:29,080 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:30:29,080 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:30:29,081 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:30:29,081 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 13:30:29,081 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:30:29,285 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 13:30:29,286 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:30:29,487 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:30:29,488 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:30:29,488 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 13:30:29,488 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 13:30:29,488 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 13:30:29,489 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 13:30:29,489 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 13:30:30,489 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 13:30:30,491 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:30:30,491 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:30:30,493 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:30:30,493 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:30:30,494 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4328332, 进程: Weixin.exe)
2025-07-30 13:30:30,495 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:30:30,496 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:30:30,498 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 13:30:30,499 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 13:30:30,499 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 13:30:30,500 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 13:30:30,500 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 13:30:30,500 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 13:30:30,501 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 13:30:31,505 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 13:30:31,505 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 13:30:31,506 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 13:30:31,506 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 13:30:31,506 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 13:30:31,506 - modules.friend_request_window - INFO - 🔧 开始移动窗口 4328332 到目标位置...
2025-07-30 13:30:32,314 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 13:30:32,314 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 13:30:32,315 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 13:30:32,315 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 13:30:32,315 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 13:30:32,315 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 13:30:32,316 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 13:30:32,316 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 13:30:32,316 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 13:30:32,316 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 13:30:32,317 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 13:30:32,317 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:30:32,317 - modules.friend_request_window - INFO -    📝 备注信息: '014325110233-张鹏飞-2025-07-30 21:29:39'
2025-07-30 13:30:32,818 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 13:30:32,819 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:30:32,820 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:30:32,820 - modules.wechat_auto_add_simple - INFO - ✅ 18501342852 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 13:30:32,820 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18501342852
2025-07-30 13:30:32,821 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:30:32,822 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:30:36,243 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2891
2025-07-30 13:30:36,244 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15717189266 (张婷)
2025-07-30 13:30:36,245 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:30:36,245 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:30:42,821 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15717189266
2025-07-30 13:30:42,822 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 13:30:42,822 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15717189266 执行添加朋友操作...
2025-07-30 13:30:42,822 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 13:30:42,823 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 13:30:42,824 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 13:30:42,825 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 13:30:42,829 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 13:30:42,830 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 13:30:42,831 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 13:30:42,832 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 13:30:42,833 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 13:30:42,833 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 13:30:42,834 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 13:30:42,836 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 13:30:42,841 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:30:42,844 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:30:42,846 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:30:42,847 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 13:30:42,853 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 13:30:42,855 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 13:30:43,357 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 13:30:43,359 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 13:30:43,426 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 13:30:43,427 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 13:30:43,434 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_133043.png
2025-07-30 13:30:43,436 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 13:30:43,437 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 13:30:43,439 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 13:30:43,445 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 13:30:43,447 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 13:30:43,451 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_133043.png
2025-07-30 13:30:43,453 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 13:30:43,454 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 13:30:43,455 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 13:30:43,456 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 13:30:43,457 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 13:30:43,464 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 13:30:43,465 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 13:30:43,466 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 13:30:43,473 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_133043.png
2025-07-30 13:30:43,478 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 13:30:43,480 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 13:30:43,484 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_133043.png
2025-07-30 13:30:43,505 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 13:30:43,513 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 13:30:43,516 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 13:30:43,517 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 13:30:43,819 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 13:30:44,588 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 13:30:44,590 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 13:30:44,593 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:30:44,593 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:30:44,593 - modules.wechat_auto_add_simple - INFO - ✅ 15717189266 添加朋友操作执行成功
2025-07-30 13:30:44,595 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:30:44,596 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:30:44,596 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 13:30:46,598 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 申请添加朋友
2025-07-30 13:30:46,599 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 13:30:46,599 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 13:30:46,599 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 13:30:46,599 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 13:30:46,600 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 13:30:46,600 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 13:30:46,600 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 13:30:46,601 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 13:30:46,601 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15717189266
2025-07-30 13:30:46,601 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 13:30:46,602 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:30:46,602 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:30:46,602 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 13:30:46,603 - modules.friend_request_window - INFO -    📱 phone: '15717189266'
2025-07-30 13:30:46,603 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 13:30:46,603 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 13:30:47,104 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 13:30:47,104 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 13:30:47,105 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 13:30:47,105 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:30:47,106 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15717189266
2025-07-30 13:30:47,106 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 13:30:47,107 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:30:47,107 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 13:30:47,108 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 13:30:47,108 - modules.friend_request_window - INFO -    📱 手机号码: 15717189266
2025-07-30 13:30:47,108 - modules.friend_request_window - INFO -    🆔 准考证: 014325110236
2025-07-30 13:30:47,109 - modules.friend_request_window - INFO -    👤 姓名: 张婷
2025-07-30 13:30:47,109 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:30:47,110 - modules.friend_request_window - INFO -    📝 备注格式: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:30:47,110 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:30:47,111 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:30:47,111 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:30:47,114 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 6094868, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 13:30:47,117 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 6094868)
2025-07-30 13:30:47,117 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 13:30:47,118 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 13:30:47,118 - modules.friend_request_window - INFO - 🔄 激活窗口: 6094868
2025-07-30 13:30:47,822 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 13:30:47,823 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 13:30:47,823 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 13:30:47,825 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 13:30:47,825 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 13:30:47,826 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:30:47,826 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 13:30:47,826 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:30:47,827 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 13:30:47,827 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 13:30:47,827 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 13:30:47,827 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 13:30:47,828 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 13:30:47,828 - modules.friend_request_window - INFO -    📝 remark参数: '014325110236-张婷-2025-07-30 21:30:47' (类型: <class 'str'>, 长度: 35)
2025-07-30 13:30:47,829 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 13:30:47,829 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:30:47,830 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 13:30:47,830 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 13:30:47,830 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 13:30:47,830 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 13:30:47,831 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 13:30:47,831 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 13:30:47,831 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 13:30:48,745 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 13:30:53,989 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 13:30:53,989 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 13:30:53,989 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 13:30:53,990 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 13:30:53,990 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:30:54,298 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:30:54,299 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:30:55,202 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:30:55,211 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:30:55,212 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 13:30:55,212 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 13:30:55,213 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 13:30:55,214 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 13:30:55,714 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 13:30:55,715 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 13:30:55,715 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 13:30:55,715 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 13:30:55,715 - modules.friend_request_window - INFO -    📝 内容: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:30:55,716 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 13:30:55,716 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110236-\xe5\xbc\xa0\xe5\xa9\xb7-2025-07-30 21:30:47'
2025-07-30 13:30:55,717 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 13:30:56,628 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 13:31:01,872 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 13:31:01,872 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 13:31:01,872 - modules.friend_request_window - INFO -    📝 原始文本: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:31:01,873 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 13:31:01,873 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:31:02,182 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:31:02,183 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:31:03,086 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:31:03,096 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:31:03,097 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:31:03,097 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 13:31:03,098 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:31:03,098 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 13:31:03,599 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:31:03,600 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 13:31:03,600 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 13:31:03,600 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 13:31:03,601 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 13:31:03,601 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 13:31:03,601 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 13:31:04,402 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 13:31:04,402 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 13:31:04,403 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 13:31:05,011 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:31:05,011 - modules.friend_request_window - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:31:05,011 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 13:31:05,012 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 13:31:05,012 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 13:31:05,529 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:05,762 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:05,994 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:06,229 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:06,461 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:06,694 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:06,926 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:07,162 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:07,394 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:07,631 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:07,865 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:08,098 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:08,329 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:08,571 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:08,804 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:09,183 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:09,831 - modules.friend_request_window - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-07-30 13:31:10,125 - modules.friend_request_window - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-07-30 13:31:10,205 - modules.friend_request_window - INFO - ✅ 未检测到频率错误，继续正常流程
2025-07-30 13:31:11,274 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:31:11,323 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 13:31:11,367 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 13:31:11,404 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 13:31:11,427 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 13:31:11,461 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:31:11,501 - modules.friend_request_window - INFO -    📝 备注信息: '014325110236-张婷-2025-07-30 21:30:47'
2025-07-30 13:31:12,082 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 13:31:12,151 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:31:12,202 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:31:12,248 - modules.wechat_auto_add_simple - INFO - ✅ 15717189266 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 13:31:12,297 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15717189266
2025-07-30 13:31:12,360 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:31:12,434 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:31:16,836 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 13:31:16,956 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 13:31:17,032 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 13:31:17,162 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 13:31:17,297 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 13:31:17,374 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 13:31:17,418 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 13:31:17,453 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 13:31:17,471 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 13:31:17,484 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 13:31:17,506 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 13:31:17,531 - __main__ - INFO - � 更新全局进度：已处理 2/2891 个联系人
2025-07-30 13:31:17,576 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 13:31:20,620 - __main__ - INFO - 
============================================================
2025-07-30 13:31:20,620 - __main__ - INFO - 🎯 开始处理第 2/2 个微信窗口 (第 1 轮)
2025-07-30 13:31:20,620 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2032818)
2025-07-30 13:31:20,621 - __main__ - INFO - 📊 全局进度：已处理 2/2891 个联系人
2025-07-30 13:31:20,621 - __main__ - INFO - ============================================================
2025-07-30 13:31:20,621 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 13:31:20,621 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2032818)
2025-07-30 13:31:20,622 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 13:31:20,622 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 13:31:20,622 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2032818)
2025-07-30 13:31:20,623 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 13:31:21,725 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 13:31:21,725 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2032818
2025-07-30 13:31:21,725 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2032818) - 增强版
2025-07-30 13:31:22,029 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:31:22,030 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:31:22,030 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:31:22,030 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:31:22,031 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:31:22,031 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:31:22,031 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:31:22,032 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:31:22,032 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:31:22,032 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:31:22,233 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:31:22,234 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:31:22,237 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2032818 (API返回: None)
2025-07-30 13:31:22,537 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:31:22,538 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 13:31:22,538 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 13:31:23,539 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 13:31:23,539 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 13:31:23,540 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 13:31:23,540 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:31:23,541 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:31:23,541 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:31:23,542 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:31:23,542 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:31:23,542 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:31:23,743 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 13:31:23,744 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:31:23,744 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:31:26,126 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:31:26,126 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:31:26,127 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 13:31:29,065 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:31:29,267 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 13:31:29,267 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:31:29,267 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:31:31,641 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:31:31,642 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:31:31,642 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 13:31:34,506 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 13:31:34,707 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 13:31:34,708 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 13:31:34,708 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 13:31:37,090 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 13:31:37,091 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 13:31:37,091 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 13:31:39,404 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 13:31:39,605 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 13:31:39,606 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 13:31:39,606 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 13:31:41,975 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 13:31:41,976 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 13:31:41,976 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 13:31:44,060 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 13:31:44,261 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 13:31:44,261 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 13:31:44,262 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 13:31:46,640 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 13:31:46,641 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 13:31:46,641 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:31:46,641 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:31:46,642 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:31:46,643 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:31:46,644 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:31:46,644 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:31:46,645 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:31:46,646 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 525644, 进程: Weixin.exe)
2025-07-30 13:31:46,646 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4328332, 进程: Weixin.exe)
2025-07-30 13:31:46,649 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 13:31:46,650 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 525644)
2025-07-30 13:31:46,651 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 525644) - 增强版
2025-07-30 13:31:46,954 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:31:46,955 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:31:46,955 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:31:46,955 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:31:46,956 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 13:31:46,956 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:31:47,159 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 13:31:47,160 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:31:47,362 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:31:47,362 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:31:47,363 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 13:31:47,363 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 13:31:47,363 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 13:31:47,363 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 13:31:47,364 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 13:31:48,365 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 13:31:48,365 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:31:48,367 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:31:48,367 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2032818, 进程: Weixin.exe)
2025-07-30 13:31:48,368 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:31:48,368 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:31:48,369 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 525644, 进程: Weixin.exe)
2025-07-30 13:31:48,370 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 4328332, 进程: Weixin.exe)
2025-07-30 13:31:48,372 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 13:31:48,373 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 13:31:48,374 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 13:31:48,374 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 13:31:48,375 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 13:31:48,377 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2032818
2025-07-30 13:31:48,378 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2032818) - 增强版
2025-07-30 13:31:48,686 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:31:48,686 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:31:48,687 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:31:48,687 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:31:48,687 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:31:48,687 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:31:48,688 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:31:48,688 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:31:48,688 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:31:48,689 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:31:48,891 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:31:48,891 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:31:48,893 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2032818 (API返回: None)
2025-07-30 13:31:49,194 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:31:49,194 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 13:31:49,194 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 13:31:49,195 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 13:31:50,195 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 13:31:50,196 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 2
2025-07-30 13:31:50,196 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 13:31:50,198 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_133150.log
2025-07-30 13:31:50,200 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:31:50,200 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 13:31:50,200 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 13:31:50,201 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 13:31:50,201 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 13:31:50,202 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 13:31:50,205 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 13:31:50,206 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 2 个
2025-07-30 13:31:50,207 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 13:31:50,207 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 13:31:50,208 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 13:31:50,208 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 13:31:50,209 - modules.wechat_auto_add_simple - INFO -   4. 添加朋友 (328x454) - add_friend
2025-07-30 13:31:50,209 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 13:31:50,211 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:31:50,211 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 4328332
2025-07-30 13:31:50,212 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 4328332) - 增强版
2025-07-30 13:31:50,534 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:31:50,535 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:31:50,535 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:31:50,535 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:31:50,536 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 13:31:50,536 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:31:50,739 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 13:31:50,740 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:31:50,941 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:31:50,942 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:31:50,946 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 4328332 (API返回: None)
2025-07-30 13:31:51,247 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:31:51,247 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 13:31:51,247 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 13:31:51,248 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 13:31:51,249 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:31:51,249 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 13:31:51,249 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 13:31:51,253 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 13:31:51,255 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 13:31:51,754 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 13:31:51,755 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:31:52,010 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2889 个
2025-07-30 13:31:52,011 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 13:31:52,011 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2887 个
2025-07-30 13:31:52,012 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2887 个 (总计: 3135 个)
2025-07-30 13:31:52,012 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 13:31:52,012 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 13:31:52,013 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:31:52,013 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:31:52,014 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 13:31:52,014 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2887
2025-07-30 13:31:52,014 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13764810759 (姜芳)
2025-07-30 13:31:52,015 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:31:52,016 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:31:58,583 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13764810759
2025-07-30 13:31:58,583 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 13:31:58,584 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13764810759 执行添加朋友操作...
2025-07-30 13:31:58,584 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 13:31:58,584 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 13:31:58,585 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 13:31:58,586 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 13:31:58,591 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 13:31:58,593 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 13:31:58,594 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 13:31:58,594 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 13:31:58,594 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 13:31:58,596 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 13:31:58,596 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 13:31:58,597 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 13:31:58,603 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:31:58,607 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:31:58,609 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:31:58,613 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:31:58,614 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 13:31:58,617 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 13:31:58,618 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 13:31:59,120 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 13:31:59,121 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 13:31:59,188 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.75, 边缘比例0.0350
2025-07-30 13:31:59,196 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_133159.png
2025-07-30 13:31:59,198 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 13:31:59,199 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 13:31:59,200 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 13:31:59,201 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 13:31:59,203 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 13:31:59,211 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_133159.png
2025-07-30 13:31:59,213 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 13:31:59,214 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 13:31:59,215 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 13:31:59,216 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 13:31:59,218 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 13:31:59,219 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 13:31:59,221 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 13:31:59,224 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 13:31:59,226 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 13:31:59,227 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 13:31:59,229 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 13:31:59,230 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 13:31:59,231 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 13:31:59,232 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 13:31:59,233 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 13:31:59,235 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 13:31:59,237 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 13:31:59,240 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 13:31:59,242 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 13:31:59,243 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 13:31:59,245 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 13:31:59,247 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 13:31:59,248 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 13:31:59,250 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 13:31:59,252 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 13:31:59,257 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 13:31:59,258 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 13:31:59,260 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 13:31:59,261 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 13:31:59,264 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 13:31:59,265 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 13:31:59,266 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 13:31:59,278 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 13:31:59,280 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 13:31:59,283 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 13:31:59,284 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 13:31:59,287 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 13:31:59,292 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 13:31:59,294 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 13:31:59,295 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 13:31:59,299 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 13:31:59,306 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 13:31:59,309 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 13:31:59,317 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_133159.png
2025-07-30 13:31:59,319 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 13:31:59,321 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 13:31:59,328 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_133159.png
2025-07-30 13:31:59,350 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 13:31:59,354 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 13:31:59,360 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 13:31:59,362 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 13:31:59,663 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 13:32:00,439 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 13:32:00,440 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 13:32:00,441 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:00,442 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:00,442 - modules.wechat_auto_add_simple - INFO - ✅ 13764810759 添加朋友操作执行成功
2025-07-30 13:32:00,443 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:00,443 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:00,443 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 13:32:02,445 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 13:32:02,445 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 13:32:02,445 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 13:32:02,446 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 13:32:02,446 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 13:32:02,447 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 13:32:02,447 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 13:32:02,447 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 13:32:02,447 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 13:32:02,448 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13764810759
2025-07-30 13:32:02,448 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 13:32:02,448 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:32:02,449 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:32:02,449 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 13:32:02,449 - modules.friend_request_window - INFO -    📱 phone: '13764810759'
2025-07-30 13:32:02,450 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 13:32:02,450 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 13:32:02,940 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 13:32:02,940 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 13:32:02,941 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 13:32:02,941 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:32:02,942 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13764810759
2025-07-30 13:32:02,943 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 13:32:02,943 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:32:02,943 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 13:32:02,944 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 13:32:02,944 - modules.friend_request_window - INFO -    📱 手机号码: 13764810759
2025-07-30 13:32:02,944 - modules.friend_request_window - INFO -    🆔 准考证: 014325110412
2025-07-30 13:32:02,945 - modules.friend_request_window - INFO -    👤 姓名: 姜芳
2025-07-30 13:32:02,945 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:32:02,945 - modules.friend_request_window - INFO -    📝 备注格式: '014325110412-姜芳-2025-07-30 21:32:02'
2025-07-30 13:32:02,945 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:32:02,946 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110412-姜芳-2025-07-30 21:32:02'
2025-07-30 13:32:02,946 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:32:02,950 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 13:32:02,951 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 13:32:02,954 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:02,954 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:02,958 - modules.wechat_auto_add_simple - INFO - ✅ 13764810759 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 13:32:02,959 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 13764810759
2025-07-30 13:32:02,961 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:02,961 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:06,499 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2887
2025-07-30 13:32:06,499 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15117930761 (南舒铭)
2025-07-30 13:32:06,500 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:06,500 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:13,064 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15117930761
2025-07-30 13:32:13,065 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 13:32:13,065 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15117930761 执行添加朋友操作...
2025-07-30 13:32:13,066 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 13:32:13,066 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 13:32:13,067 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 13:32:13,069 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 13:32:13,078 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 13:32:13,081 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 13:32:13,081 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 13:32:13,082 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 13:32:13,082 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 13:32:13,082 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 13:32:13,083 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 13:32:13,083 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 13:32:13,090 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:32:13,095 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:32:13,097 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:32:13,100 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:32:13,104 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 13:32:13,109 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 13:32:13,112 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 13:32:13,614 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 13:32:13,615 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 13:32:13,672 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差35.57, 边缘比例0.0368
2025-07-30 13:32:13,682 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_133213.png
2025-07-30 13:32:13,684 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 13:32:13,689 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 13:32:13,691 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 13:32:13,694 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 13:32:13,695 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 13:32:13,701 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_133213.png
2025-07-30 13:32:13,706 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 13:32:13,709 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 13:32:13,711 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 13:32:13,714 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 13:32:13,715 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 13:32:13,716 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 13:32:13,718 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 13:32:13,722 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 13:32:13,733 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_133213.png
2025-07-30 13:32:13,736 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 13:32:13,741 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 13:32:13,747 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_133213.png
2025-07-30 13:32:13,776 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 13:32:13,780 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 13:32:13,783 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 13:32:13,786 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 13:32:14,093 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 13:32:14,872 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 13:32:14,873 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 13:32:14,875 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:14,876 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:14,876 - modules.wechat_auto_add_simple - INFO - ✅ 15117930761 添加朋友操作执行成功
2025-07-30 13:32:14,878 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:14,879 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:14,879 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 13:32:16,882 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 13:32:16,882 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 13:32:16,883 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 13:32:16,883 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 13:32:16,883 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 13:32:16,884 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 13:32:16,884 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 13:32:16,884 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 13:32:16,884 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 13:32:16,885 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15117930761
2025-07-30 13:32:16,885 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 13:32:16,886 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:32:16,886 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:32:16,886 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 13:32:16,887 - modules.friend_request_window - INFO -    📱 phone: '15117930761'
2025-07-30 13:32:16,887 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 13:32:16,887 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 13:32:17,396 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 13:32:17,396 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 13:32:17,397 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 13:32:17,397 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:32:17,398 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15117930761
2025-07-30 13:32:17,399 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 13:32:17,399 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:32:17,400 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 13:32:17,400 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 13:32:17,400 - modules.friend_request_window - INFO -    📱 手机号码: 15117930761
2025-07-30 13:32:17,401 - modules.friend_request_window - INFO -    🆔 准考证: 014325110414
2025-07-30 13:32:17,401 - modules.friend_request_window - INFO -    👤 姓名: 南舒铭
2025-07-30 13:32:17,402 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:32:17,402 - modules.friend_request_window - INFO -    📝 备注格式: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:17,403 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:32:17,403 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:17,404 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:32:17,406 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 7408834, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 13:32:17,409 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 7408834)
2025-07-30 13:32:17,410 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 13:32:17,411 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 13:32:17,412 - modules.friend_request_window - INFO - 🔄 激活窗口: 7408834
2025-07-30 13:32:18,115 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 13:32:18,116 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 13:32:18,116 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 13:32:18,117 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 13:32:18,117 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 13:32:18,117 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:32:18,118 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 13:32:18,118 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:32:18,118 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 13:32:18,119 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 13:32:18,119 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 13:32:18,119 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 13:32:18,120 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 13:32:18,120 - modules.friend_request_window - INFO -    📝 remark参数: '014325110414-南舒铭-2025-07-30 21:32:17' (类型: <class 'str'>, 长度: 36)
2025-07-30 13:32:18,120 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 13:32:18,120 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:18,121 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 13:32:18,121 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 13:32:18,121 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 13:32:18,122 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 13:32:18,122 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 13:32:18,123 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 13:32:18,123 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 13:32:19,040 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 13:32:24,281 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 13:32:24,282 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 13:32:24,282 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 13:32:24,282 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 13:32:24,283 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:32:24,592 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:32:24,592 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:32:25,495 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:32:25,505 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:32:25,507 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 13:32:25,507 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 13:32:25,508 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 13:32:25,508 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 13:32:26,009 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 13:32:26,009 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 13:32:26,010 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 13:32:26,010 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 13:32:26,010 - modules.friend_request_window - INFO -    📝 内容: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:26,010 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 13:32:26,011 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110414-\xe5\x8d\x97\xe8\x88\x92\xe9\x93\xad-2025-07-30 21:32:17'
2025-07-30 13:32:26,011 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 13:32:26,921 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 13:32:32,165 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 13:32:32,165 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 13:32:32,165 - modules.friend_request_window - INFO -    📝 原始文本: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:32,166 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 13:32:32,166 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:32:32,477 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:32:32,478 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:32:33,380 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:32:33,390 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:32:33,390 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:33,391 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 13:32:33,391 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:33,392 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 13:32:33,893 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:32:33,893 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 13:32:33,893 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 13:32:33,894 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 13:32:33,894 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 13:32:33,894 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 13:32:33,894 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 13:32:34,695 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 13:32:34,695 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 13:32:34,696 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 13:32:35,304 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:32:35,305 - modules.friend_request_window - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:32:35,305 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 13:32:35,305 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 13:32:35,306 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 13:32:35,807 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 13:32:35,809 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 13:32:35,810 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 13:32:35,810 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 13:32:35,812 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 13:32:35,812 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 13:32:35,813 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 13:32:35,819 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 13:32:35,821 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 13:32:35,821 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:32:35,822 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 13:32:35,822 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 13:32:35,823 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 13:32:35,823 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 13:32:35,823 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 13:32:35,823 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 13:32:35,824 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:32:35,824 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 13:32:35,828 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 13:32:35,829 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 13:32:35,829 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 13:32:36,331 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 13:32:36,332 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 13:32:36,332 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 13:32:36,332 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 13:32:36,333 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 13:32:36,333 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 13:32:36,333 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 13:32:36,334 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 13:32:37,255 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 13:32:37,255 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 13:32:37,256 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 13:32:37,256 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 13:32:37,273 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 13:32:37,274 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 13:32:38,075 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 13:32:38,075 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 13:32:38,075 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 13:32:38,076 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 13:32:38,877 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 13:32:38,877 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 13:32:38,877 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 2/2
2025-07-30 13:32:38,878 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 13:32:38,878 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 13:32:38,878 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 13:32:38,879 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 13:32:38,879 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 13:32:40,380 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 13:32:40,380 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 13:32:40,381 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 13:32:40,381 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 13:32:42,382 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 13:32:42,383 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:32:42,383 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 13:32:42,384 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:32:42,385 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:32:42,385 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:32:42,388 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 13:32:42,390 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 13:32:42,392 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 13:32:42,414 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 13:32:42,414 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 13:32:42,415 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 13:32:42,416 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 1246274
2025-07-30 13:32:42,417 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:32:42,418 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1246274) - 增强版
2025-07-30 13:32:42,724 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:32:42,725 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:32:42,725 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:32:42,726 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:32:42,726 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:32:42,726 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:32:42,727 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:32:42,727 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:32:42,728 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:32:42,728 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:32:42,929 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:32:42,930 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:32:42,930 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 13:32:42,931 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 13:32:42,931 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 13:32:42,931 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 13:32:42,932 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 13:32:44,932 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 13:32:44,934 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:32:44,934 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 13:32:44,934 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 13:32:44,934 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 13:32:44,935 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 13:32:44,935 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:32:44,935 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:32:44,935 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:32:44,936 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:32:44,936 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:32:44,936 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:32:45,137 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 13:32:45,137 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:32:45,138 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:32:47,520 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:32:47,521 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:32:47,521 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 13:32:50,314 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:32:50,515 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 13:32:50,516 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:32:50,516 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:32:52,887 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:32:52,887 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:32:52,887 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 13:32:55,224 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 13:32:55,425 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 13:32:55,425 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 13:32:55,426 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 13:32:57,794 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 13:32:57,795 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 13:32:57,795 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 13:32:59,736 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 13:32:59,937 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 13:32:59,937 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 13:32:59,938 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 13:33:02,306 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 13:33:02,307 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 13:33:02,307 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 13:33:04,378 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 13:33:04,579 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 13:33:04,579 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 13:33:04,579 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 13:33:06,952 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 13:33:06,952 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 13:33:06,953 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:33:06,954 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:33:06,954 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:33:06,954 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:33:06,956 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:33:06,956 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:33:06,957 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1705300, 进程: Weixin.exe)
2025-07-30 13:33:06,958 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 13:33:06,959 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1705300)
2025-07-30 13:33:06,959 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1705300) - 增强版
2025-07-30 13:33:07,262 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:33:07,263 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:33:07,263 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:33:07,264 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:33:07,264 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 13:33:07,264 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:33:07,469 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 13:33:07,469 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:33:07,671 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:33:07,671 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:33:07,672 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 13:33:07,672 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 13:33:07,672 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 13:33:07,672 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 13:33:07,673 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 13:33:08,674 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 13:33:08,675 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:33:08,675 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:33:08,677 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:33:08,677 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:33:08,678 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1705300, 进程: Weixin.exe)
2025-07-30 13:33:08,679 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 13:33:08,680 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 13:33:08,680 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 13:33:08,681 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 13:33:08,682 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 13:33:08,682 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 13:33:08,683 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 13:33:09,686 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 13:33:09,686 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 13:33:09,686 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 13:33:09,687 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 13:33:09,687 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 13:33:09,687 - modules.friend_request_window - INFO - 🔧 开始移动窗口 1705300 到目标位置...
2025-07-30 13:33:10,491 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 13:33:10,491 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 13:33:10,491 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 13:33:10,492 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 13:33:10,492 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 13:33:10,492 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 13:33:10,492 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 13:33:10,493 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 13:33:10,493 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 13:33:10,493 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 13:33:10,494 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 13:33:10,494 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:33:10,494 - modules.friend_request_window - INFO -    📝 备注信息: '014325110414-南舒铭-2025-07-30 21:32:17'
2025-07-30 13:33:10,995 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 13:33:10,996 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:33:10,996 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:33:10,997 - modules.wechat_auto_add_simple - INFO - ✅ 15117930761 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 13:33:10,997 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15117930761
2025-07-30 13:33:10,998 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:33:10,999 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:33:12,468 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 13:33:12,468 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 13:33:12,468 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 13:33:12,469 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 13:33:12,470 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 13:33:12,470 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 13:33:12,471 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 13:33:12,471 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 13:33:12,471 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 13:33:12,471 - __main__ - INFO - 🔄 第 2 个微信窗口达到联系人处理数量限制
2025-07-30 13:33:12,472 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 13:33:12,473 - __main__ - INFO - � 更新全局进度：已处理 4/2891 个联系人
2025-07-30 13:33:12,473 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 13:33:15,474 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 13:33:15,474 - __main__ - INFO - 📊 当前进度：已处理 4/2891 个联系人
2025-07-30 13:33:15,474 - __main__ - INFO - 
============================================================
2025-07-30 13:33:15,475 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 2 轮)
2025-07-30 13:33:15,475 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1246274)
2025-07-30 13:33:15,475 - __main__ - INFO - 📊 全局进度：已处理 4/2891 个联系人
2025-07-30 13:33:15,476 - __main__ - INFO - ============================================================
2025-07-30 13:33:15,476 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 13:33:15,476 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1246274)
2025-07-30 13:33:15,476 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 13:33:15,477 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 13:33:15,477 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 1246274)
2025-07-30 13:33:15,477 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 13:33:16,578 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 13:33:16,579 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1246274
2025-07-30 13:33:16,579 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1246274) - 增强版
2025-07-30 13:33:16,884 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:33:16,884 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:33:16,885 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:33:16,885 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:33:16,885 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:33:16,885 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:33:16,886 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:33:16,886 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:33:16,886 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:33:16,887 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:33:17,089 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:33:17,089 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:33:17,090 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1246274 (API返回: None)
2025-07-30 13:33:17,391 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:33:17,392 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 13:33:17,392 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 13:33:18,393 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 13:33:18,393 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 13:33:18,393 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 13:33:18,394 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:33:18,394 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:33:18,394 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:33:18,395 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:33:18,395 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:33:18,395 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:33:18,596 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 13:33:18,597 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:33:18,597 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:33:20,968 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:33:20,969 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:33:20,969 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 13:33:22,703 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:33:22,904 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 13:33:22,905 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:33:22,905 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:33:25,274 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:33:25,274 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:33:25,274 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 13:33:27,358 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 13:33:27,559 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 13:33:27,560 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 13:33:27,560 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 13:33:29,934 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 13:33:29,935 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 13:33:29,935 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 13:33:32,077 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 13:33:32,278 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 13:33:32,278 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 13:33:32,279 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 13:33:34,649 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 13:33:34,649 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 13:33:34,649 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 13:33:37,571 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 13:33:37,772 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 13:33:37,773 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 13:33:37,773 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 13:33:40,150 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 13:33:40,151 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 13:33:40,151 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:33:40,151 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 13:33:40,152 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:33:40,153 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:33:40,153 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:33:40,155 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1705300, 进程: Weixin.exe)
2025-07-30 13:33:40,157 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 13:33:40,157 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 1705300)
2025-07-30 13:33:40,158 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1705300) - 增强版
2025-07-30 13:33:40,462 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:33:40,462 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:33:40,463 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:33:40,463 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:33:40,463 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 13:33:40,464 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:33:40,667 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 13:33:40,668 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:33:40,870 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:33:40,870 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:33:40,871 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 13:33:40,871 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 13:33:40,871 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 13:33:40,871 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 13:33:40,872 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 13:33:41,872 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 13:33:41,873 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:33:41,874 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 13:33:41,874 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1246274, 进程: Weixin.exe)
2025-07-30 13:33:41,875 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 1705300, 进程: Weixin.exe)
2025-07-30 13:33:41,878 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 13:33:41,878 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 13:33:41,879 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 13:33:41,880 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 13:33:41,880 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 13:33:41,880 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1246274
2025-07-30 13:33:41,882 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1246274) - 增强版
2025-07-30 13:33:42,189 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:33:42,190 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:33:42,190 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:33:42,190 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:33:42,190 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:33:42,191 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:33:42,191 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:33:42,192 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:33:42,192 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:33:42,192 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:33:42,394 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:33:42,394 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:33:42,396 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1246274 (API返回: None)
2025-07-30 13:33:42,697 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:33:42,697 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 13:33:42,697 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 13:33:42,698 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 13:33:43,698 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 13:33:43,699 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 13:33:43,699 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 13:33:43,701 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_133343.log
2025-07-30 13:33:43,702 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:33:43,702 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 13:33:43,703 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 13:33:43,703 - __main__ - INFO - 🔄 传递全局联系人索引: 4
2025-07-30 13:33:43,703 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 13:33:43,704 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 13:33:43,706 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 13:33:43,707 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 13:33:43,708 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 13:33:43,708 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 13:33:43,709 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 13:33:43,710 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 13:33:43,711 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:33:43,712 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1705300
2025-07-30 13:33:43,712 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1705300) - 增强版
2025-07-30 13:33:44,021 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:33:44,021 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:33:44,022 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 13:33:44,022 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 13:33:44,022 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 13:33:44,023 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 13:33:44,023 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 13:33:44,023 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 13:33:44,225 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:33:44,226 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:33:44,231 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1705300 (API返回: None)
2025-07-30 13:33:44,532 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 13:33:44,533 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 13:33:44,533 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 13:33:44,533 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 13:33:44,534 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:33:44,535 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 13:33:44,535 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 13:33:44,539 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 13:33:44,540 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 13:33:45,041 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 13:33:45,041 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:33:45,293 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2887 个
2025-07-30 13:33:45,294 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 5 个联系人开始处理
2025-07-30 13:33:45,294 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2883 个
2025-07-30 13:33:45,294 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2883 个 (总计: 3135 个)
2025-07-30 13:33:45,295 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 13:33:45,295 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 13:33:45,296 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:33:45,296 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:33:45,296 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 13:33:45,297 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2883
2025-07-30 13:33:45,297 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17519195651 (张川)
2025-07-30 13:33:45,298 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:33:45,298 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:33:51,878 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17519195651
2025-07-30 13:33:51,878 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 13:33:51,879 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17519195651 执行添加朋友操作...
2025-07-30 13:33:51,879 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 13:33:51,880 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 13:33:51,880 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 13:33:51,882 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 13:33:51,887 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 13:33:51,890 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 13:33:51,891 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 13:33:51,892 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 13:33:51,892 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 13:33:51,893 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 13:33:51,893 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 13:33:51,894 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 13:33:51,898 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 13:33:51,903 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 13:33:51,907 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 13:33:51,909 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 13:33:51,912 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 13:33:52,417 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 13:33:52,420 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 13:33:52,496 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差38.56, 边缘比例0.0352
2025-07-30 13:33:52,578 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_133352.png
2025-07-30 13:33:52,588 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 13:33:52,592 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 13:33:52,594 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 13:33:52,598 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 13:33:52,618 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 13:33:52,639 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_133352.png
2025-07-30 13:33:52,642 - WeChatAutoAdd - INFO - 底部区域原始检测到 2 个轮廓
2025-07-30 13:33:52,644 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 13:33:52,651 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 13:33:52,657 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 13:33:52,659 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 13:33:52,661 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 13:33:52,668 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 13:33:52,672 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 13:33:52,683 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_133352.png
2025-07-30 13:33:52,686 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 13:33:52,688 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 13:33:52,694 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_133352.png
2025-07-30 13:33:52,720 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 13:33:52,723 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 13:33:52,725 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 13:33:52,728 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 13:33:53,031 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 13:33:53,815 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 13:33:53,817 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 13:33:53,822 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:33:53,823 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:33:53,824 - modules.wechat_auto_add_simple - INFO - ✅ 17519195651 添加朋友操作执行成功
2025-07-30 13:33:53,825 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:33:53,826 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:33:53,826 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 13:33:55,828 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 13:33:55,828 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 13:33:55,829 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 13:33:55,829 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 13:33:55,829 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 13:33:55,829 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 13:33:55,830 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 13:33:55,830 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 13:33:55,830 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 13:33:55,831 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17519195651
2025-07-30 13:33:55,831 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 13:33:55,831 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:33:55,832 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:33:55,832 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 13:33:55,832 - modules.friend_request_window - INFO -    📱 phone: '17519195651'
2025-07-30 13:33:55,833 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 13:33:55,833 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 13:33:56,340 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 13:33:56,341 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 13:33:56,341 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 13:33:56,341 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 13:33:56,342 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17519195651
2025-07-30 13:33:56,343 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 13:33:56,343 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:33:56,344 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 13:33:56,344 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 13:33:56,344 - modules.friend_request_window - INFO -    📱 手机号码: 17519195651
2025-07-30 13:33:56,345 - modules.friend_request_window - INFO -    🆔 准考证: 014325110417
2025-07-30 13:33:56,345 - modules.friend_request_window - INFO -    👤 姓名: 张川
2025-07-30 13:33:56,345 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 13:33:56,345 - modules.friend_request_window - INFO -    📝 备注格式: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:33:56,346 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 13:33:56,347 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:33:56,347 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 13:33:56,348 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2886746, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 13:33:56,351 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2886746)
2025-07-30 13:33:56,351 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 13:33:56,351 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 13:33:56,354 - modules.friend_request_window - INFO - 🔄 激活窗口: 2886746
2025-07-30 13:33:57,056 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 13:33:57,057 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 13:33:57,057 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 13:33:57,058 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 13:33:57,058 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 13:33:57,058 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 13:33:57,058 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 13:33:57,059 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 13:33:57,059 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 13:33:57,059 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 13:33:57,059 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 13:33:57,060 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 13:33:57,060 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 13:33:57,060 - modules.friend_request_window - INFO -    📝 remark参数: '014325110417-张川-2025-07-30 21:33:56' (类型: <class 'str'>, 长度: 35)
2025-07-30 13:33:57,061 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 13:33:57,061 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:33:57,061 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 13:33:57,061 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 13:33:57,062 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 13:33:57,062 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 13:33:57,063 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 13:33:57,063 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 13:33:57,064 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 13:33:57,983 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 13:34:03,228 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 13:34:03,229 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 13:34:03,229 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 13:34:03,229 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 13:34:03,230 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:34:03,540 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:34:03,540 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:34:04,442 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:34:04,452 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:34:04,453 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 13:34:04,454 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 13:34:04,454 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 13:34:04,455 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 13:34:04,956 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 13:34:04,957 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 13:34:04,957 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 13:34:04,957 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 13:34:04,958 - modules.friend_request_window - INFO -    📝 内容: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:34:04,958 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 13:34:04,958 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110417-\xe5\xbc\xa0\xe5\xb7\x9d-2025-07-30 21:33:56'
2025-07-30 13:34:04,958 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 13:34:05,865 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 13:34:11,110 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 13:34:11,110 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 13:34:11,110 - modules.friend_request_window - INFO -    📝 原始文本: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:34:11,111 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 13:34:11,111 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: 'main_controller.py...' (前50字符)
2025-07-30 13:34:11,421 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 13:34:11,422 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 13:34:12,324 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 13:34:12,335 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 13:34:12,335 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:34:12,336 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 13:34:12,337 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:34:12,337 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 13:34:12,838 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110417-张川-2025-07-30 21:33:56'
2025-07-30 13:34:12,838 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 13:34:12,839 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 13:34:12,839 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 13:34:12,839 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 13:34:12,840 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 13:34:12,841 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 13:34:13,641 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 13:34:13,642 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 13:34:13,642 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 13:34:14,248 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 13:34:14,248 - modules.friend_request_window - INFO - 📋 当前黑名单中有 3 个窗口
2025-07-30 13:34:14,248 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 13:34:14,249 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 13:34:14,249 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 13:34:14,751 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 13:34:14,753 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 13:34:14,753 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 13:34:14,754 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 13:34:14,754 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 13:34:14,754 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 13:34:14,754 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 13:34:14,755 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 13:34:14,755 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 13:34:14,755 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:34:14,756 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 13:34:14,756 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 13:34:14,756 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 13:34:14,756 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 13:34:14,757 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 13:34:14,757 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 13:34:14,758 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 13:34:14,759 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 13:34:14,765 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 13:34:14,765 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 13:34:14,765 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 13:34:15,268 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 13:34:15,269 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 13:34:15,269 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 13:34:15,269 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 13:34:15,270 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 13:34:15,270 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 13:34:15,270 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 13:34:15,271 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 13:34:16,182 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 13:34:16,183 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 13:34:16,183 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 13:34:16,183 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 13:34:16,201 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 13:34:16,202 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 13:34:17,003 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 13:34:17,003 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 13:34:17,003 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 13:34:17,004 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 13:34:17,004 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 13:34:17,005 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 13:34:17,005 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 13:34:17,005 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 13:34:18,506 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 13:34:18,506 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 13:34:18,507 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 13:34:18,507 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 13:34:20,508 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 13:34:20,509 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:34:20,509 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 13:34:20,509 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 13:34:20,512 - modules.window_manager - INFO - 🎯 总共找到 0 个微信窗口
2025-07-30 13:34:20,513 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 13:34:20,513 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 13:34:20,536 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 13:34:20,536 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 13:34:20,536 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 13:34:20,537 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 2032818
2025-07-30 13:34:20,538 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:34:20,539 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2032818) - 增强版
2025-07-30 13:34:20,539 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 13:34:21,063 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 13:34:21,064 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 13:34:21,064 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 13:34:21,065 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 13:34:21,065 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 13:34:21,066 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 13:34:21,066 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 13:34:21,067 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 13:34:21,067 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 13:34:21,067 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 13:34:21,269 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 13:34:21,269 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 13:34:21,270 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 13:34:21,270 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 13:34:21,270 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 13:34:21,270 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 13:34:21,271 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 13:34:23,271 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 13:34:23,273 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 13:34:23,273 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 13:34:23,273 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 13:34:23,274 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 13:34:23,274 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 13:34:23,274 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 13:34:23,275 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 13:34:23,275 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 13:34:23,275 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 13:34:23,275 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 13:34:23,276 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 13:34:23,476 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 13:34:23,477 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 13:34:23,477 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 13:34:25,848 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 13:34:25,849 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 13:34:25,849 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 13:34:28,387 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 13:34:28,591 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 13:34:28,591 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 13:34:28,592 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 13:34:30,958 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 13:34:30,958 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 13:34:30,959 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
