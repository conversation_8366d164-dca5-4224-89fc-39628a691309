2025-07-30 12:32:03,722 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:32:03,722 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:32:03,723 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:32:03,724 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:32:03,724 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:32:03,725 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:32:03,726 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 12:32:03,727 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:32:03,727 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:32:03,728 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:32:03,729 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:32:03,729 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:32:03,734 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:32:03,739 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_123203.log
2025-07-30 12:32:03,741 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:32:03,742 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:32:03,743 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:32:03,743 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 12:32:03,744 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 12:32:03,744 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 12:32:03,744 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 20:32:03
2025-07-30 12:32:03,745 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 12:32:03,745 - __main__ - INFO - 📅 启动时间: 2025-07-30 20:32:03
2025-07-30 12:32:03,747 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 12:32:03,748 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:32:04,287 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:32:04,287 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:32:04,823 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:32:04,823 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:32:04,824 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:32:04,825 - __main__ - INFO - ✅ 找到 2 个可用微信窗口
2025-07-30 12:32:04,826 - __main__ - INFO -   窗口 1: 微信 (句柄: 263568)
2025-07-30 12:32:04,827 - __main__ - INFO -   窗口 2: 微信 (句柄: 9045874)
2025-07-30 12:32:04,828 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 12:32:04,828 - __main__ - INFO - 📊 需要移动的窗口数量: 2
2025-07-30 12:32:04,829 - __main__ - INFO - 🔄 移动窗口 1/2: 微信 (句柄: 263568)
2025-07-30 12:32:04,832 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 12:32:04,833 - __main__ - WARNING - ⚠️ 窗口 1 移动失败
2025-07-30 12:32:05,133 - __main__ - INFO - 🔄 移动窗口 2/2: 微信 (句柄: 9045874)
2025-07-30 12:32:05,137 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 12:32:05,137 - __main__ - WARNING - ⚠️ 窗口 2 移动失败
2025-07-30 12:32:05,138 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 12:32:05,138 - __main__ - INFO -   ✅ 成功移动: 0 个窗口
2025-07-30 12:32:05,138 - __main__ - INFO -   ❌ 移动失败: 2 个窗口
2025-07-30 12:32:05,139 - __main__ - INFO -   📈 成功率: 0.0%
2025-07-30 12:32:05,139 - __main__ - ERROR - ❌ 所有窗口移动都失败了
2025-07-30 12:32:05,140 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 12:32:06,026 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 12:32:06,027 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 12:32:06,027 - __main__ - INFO - 📋 待处理联系人数: 2899
2025-07-30 12:32:06,028 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 12:32:06,029 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2899
2025-07-30 12:32:06,029 - __main__ - INFO - 
============================================================
2025-07-30 12:32:06,029 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 12:32:06,030 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:32:06,030 - __main__ - INFO - 📊 全局进度：已处理 0/2899 个联系人
2025-07-30 12:32:06,030 - __main__ - INFO - ============================================================
2025-07-30 12:32:06,031 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 12:32:06,031 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 263568)
2025-07-30 12:32:06,031 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 12:32:06,032 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 12:32:06,134 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:32:06,135 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:32:06,439 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:32:06,440 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:32:06,440 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:32:06,440 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:32:06,441 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:32:06,441 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:32:06,441 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:32:06,442 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:32:06,442 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:32:06,442 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:32:06,644 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:32:06,645 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:32:06,646 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:32:06,947 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:32:06,947 - __main__ - INFO - ✅ 步骤1：窗口管理完成（窗口已激活，位置已在批量移动阶段设置）
2025-07-30 12:32:06,948 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 12:32:07,949 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 12:32:07,949 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 12:32:07,949 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:32:07,949 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:32:07,950 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:32:07,950 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:32:07,950 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:32:07,951 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:32:08,151 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:32:08,152 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:32:10,545 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:32:10,545 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:32:10,546 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 12:32:12,522 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:32:12,722 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:32:12,723 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:32:15,091 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:32:15,092 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:32:15,092 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 3.0 秒...
2025-07-30 12:32:18,081 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:32:18,282 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:32:18,282 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:32:20,662 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:32:20,663 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:32:20,663 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 12:32:23,418 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:32:23,619 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:32:23,620 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:32:25,988 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:32:25,989 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:32:25,989 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 12:32:27,690 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:32:27,891 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:32:27,892 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:32:30,277 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:32:30,278 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:32:30,278 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:32:30,279 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:32:30,279 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:32:30,281 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:32:30,282 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:32:30,283 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2034782, 进程: Weixin.exe)
2025-07-30 12:32:30,284 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:32:30,285 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:32:30,288 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:32:30,289 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 2034782)
2025-07-30 12:32:30,291 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2034782) - 增强版
2025-07-30 12:32:30,596 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:32:30,596 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:32:30,597 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:32:30,597 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:32:30,598 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 12:32:30,598 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:32:30,802 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 12:32:30,802 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:32:31,004 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:32:31,004 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:32:31,004 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 12:32:31,005 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 12:32:31,005 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 12:32:31,005 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:32:31,005 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 12:32:32,006 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 12:32:32,007 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:32:32,008 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:32:32,009 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:32:32,010 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2034782, 进程: Weixin.exe)
2025-07-30 12:32:32,011 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:32:32,011 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:32:32,014 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 12:32:32,015 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 12:32:32,015 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 12:32:32,015 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 12:32:32,016 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 263568
2025-07-30 12:32:32,016 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:32:32,326 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:32:32,327 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:32:32,327 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:32:32,328 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:32:32,328 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:32:32,328 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:32:32,329 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:32:32,329 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:32:32,329 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:32:32,330 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:32:32,531 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:32:32,531 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:32:32,534 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 263568 (API返回: None)
2025-07-30 12:32:32,834 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:32:32,835 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 12:32:32,835 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 12:32:33,835 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 12:32:33,836 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 12:32:33,836 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 12:32:33,839 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_123233.log
2025-07-30 12:32:33,841 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:32:33,841 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 12:32:33,842 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 12:32:33,843 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 12:32:33,843 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 12:32:33,844 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 12:32:33,846 - modules.wechat_auto_add_simple - INFO - 🎯 找到 3 个微信窗口:
2025-07-30 12:32:33,846 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 12:32:33,846 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 2 个
2025-07-30 12:32:33,847 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 12:32:33,848 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 12:32:33,848 - modules.wechat_auto_add_simple - INFO -   3. 微信 (726x650) - main
2025-07-30 12:32:33,849 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 12:32:33,851 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:32:33,851 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2034782
2025-07-30 12:32:33,851 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2034782) - 增强版
2025-07-30 12:32:34,163 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:32:34,163 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:32:34,164 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 12:32:34,164 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 12:32:34,164 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 12:32:34,165 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 12:32:34,165 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 12:32:34,165 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 12:32:34,367 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:32:34,368 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:32:34,371 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2034782 (API返回: None)
2025-07-30 12:32:34,673 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 12:32:34,673 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 12:32:34,673 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 12:32:34,674 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 12:32:34,676 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:32:34,676 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 12:32:34,676 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 12:32:34,681 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 12:32:34,682 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 12:32:35,363 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 12:32:35,364 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:32:35,718 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2901 个
2025-07-30 12:32:35,720 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2901 个 (总计: 3135 个)
2025-07-30 12:32:35,720 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 12:32:35,720 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 12:32:35,722 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:32:35,722 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:32:35,723 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 12:32:35,723 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2901
2025-07-30 12:32:35,724 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 13580982528 (钟建强)
2025-07-30 12:32:35,726 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:32:35,727 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:32:42,316 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 13580982528
2025-07-30 12:32:42,317 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 12:32:42,318 - modules.wechat_auto_add_simple - INFO - 👥 开始为 13580982528 执行添加朋友操作...
2025-07-30 12:32:42,318 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 12:32:42,319 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 12:32:42,320 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 12:32:42,321 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 12:32:42,330 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 12:32:42,334 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 12:32:42,335 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 12:32:42,338 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 12:32:42,348 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 12:32:42,356 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 12:32:42,360 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 12:32:42,362 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 12:32:42,380 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 12:32:42,384 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:32:42,394 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 12:32:42,399 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 12:32:42,401 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 12:32:42,402 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 12:32:42,907 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 12:32:42,908 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 12:32:42,994 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差46.24, 边缘比例0.0618
2025-07-30 12:32:43,006 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_123242.png
2025-07-30 12:32:43,008 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 12:32:43,009 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 12:32:43,010 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 12:32:43,011 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 12:32:43,013 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 12:32:43,020 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_123243.png
2025-07-30 12:32:43,022 - WeChatAutoAdd - INFO - 底部区域原始检测到 51 个轮廓
2025-07-30 12:32:43,023 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,321), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 12:32:43,025 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=321 (距底部154像素区域)
2025-07-30 12:32:43,028 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 12:32:43,029 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,321), 尺寸128x30
2025-07-30 12:32:43,031 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,321), 尺寸128x30
2025-07-30 12:32:43,033 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(134,273), 尺寸6x6, 长宽比1.00, 面积36
2025-07-30 12:32:43,034 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,272), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,036 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(133,270), 尺寸3x4, 长宽比0.75, 面积12
2025-07-30 12:32:43,039 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,258), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 12:32:43,046 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,257), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:32:43,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,256), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 12:32:43,059 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,256), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,060 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(262,255), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:32:43,063 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 12:32:43,070 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,254), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 12:32:43,073 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,083 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,085 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,252), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,087 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,252), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:32:43,098 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,099 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(253,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,102 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 12:32:43,104 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(242,250), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:32:43,111 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,113 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,248), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 12:32:43,114 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,248), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 12:32:43,116 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,248), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 12:32:43,118 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:32:43,120 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:32:43,125 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=65.3 (阈值:60)
2025-07-30 12:32:43,130 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,247), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 12:32:43,132 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=66.7 (阈值:60)
2025-07-30 12:32:43,133 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,247), 尺寸34x35, 长宽比0.97, 面积1190
2025-07-30 12:32:43,134 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,240), 尺寸11x3, 长宽比3.67, 面积33
2025-07-30 12:32:43,135 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(243,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:32:43,138 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(238,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:32:43,142 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(236,239), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:32:43,146 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(170,239), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:32:43,148 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(163,239), 尺寸5x2, 长宽比2.50, 面积10
2025-07-30 12:32:43,149 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(154,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,150 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(132,239), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,152 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,238), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 12:32:43,153 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(150,235), 尺寸1x7, 长宽比0.14, 面积7
2025-07-30 12:32:43,155 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,235), 尺寸3x8, 长宽比0.38, 面积24
2025-07-30 12:32:43,158 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,234), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 12:32:43,162 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(149,233), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 12:32:43,164 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(123,233), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 12:32:43,164 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(141,232), 尺寸1x3, 长宽比0.33, 面积3
2025-07-30 12:32:43,165 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,231), 尺寸4x2, 长宽比2.00, 面积8
2025-07-30 12:32:43,167 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(223,230), 尺寸12x12, 长宽比1.00, 面积144
2025-07-30 12:32:43,180 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(152,230), 尺寸11x11, 长宽比1.00, 面积121
2025-07-30 12:32:43,188 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=77.7 (阈值:60)
2025-07-30 12:32:43,196 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(265,229), 尺寸14x14, 长宽比1.00, 面积196
2025-07-30 12:32:43,198 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,229), 尺寸28x15, 长宽比1.87, 面积420
2025-07-30 12:32:43,199 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,229), 尺寸26x14, 长宽比1.86, 面积364
2025-07-30 12:32:43,204 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=98.7 (阈值:60)
2025-07-30 12:32:43,204 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(127,229), 尺寸7x1, 长宽比7.00, 面积7
2025-07-30 12:32:43,208 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,229), 尺寸5x5, 长宽比1.00, 面积25
2025-07-30 12:32:43,212 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 12:32:43,213 - WeChatAutoAdd - INFO - 底部区域找到 7 个按钮候选
2025-07-30 12:32:43,214 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=321, 很可能是'添加到通讯录'按钮
2025-07-30 12:32:43,215 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 336), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 12:32:43,217 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 12:32:43,232 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_123243.png
2025-07-30 12:32:43,233 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 12:32:43,234 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 12:32:43,536 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 336) -> 屏幕坐标(1364, 336)
2025-07-30 12:32:44,309 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 12:32:44,310 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 12:32:44,311 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:32:44,312 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:32:44,312 - modules.wechat_auto_add_simple - INFO - ✅ 13580982528 添加朋友操作执行成功
2025-07-30 12:32:44,313 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:32:44,314 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:32:44,314 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 12:32:46,316 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 12:32:46,317 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 12:32:46,317 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 12:32:46,317 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 12:32:46,318 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 12:32:46,318 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 12:32:46,318 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 12:32:46,319 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 12:32:46,319 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 12:32:46,319 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 13580982528
2025-07-30 12:32:46,324 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 12:32:46,324 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:32:46,324 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:32:46,325 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 12:32:46,325 - modules.friend_request_window - INFO -    📱 phone: '13580982528'
2025-07-30 12:32:46,326 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 12:32:46,326 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 12:32:46,947 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 12:32:46,948 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 12:32:46,948 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 12:32:46,948 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 12:32:46,950 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 13580982528
2025-07-30 12:32:46,951 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 12:32:46,951 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:32:46,953 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 12:32:46,953 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 12:32:46,953 - modules.friend_request_window - INFO -    📱 手机号码: 13580982528
2025-07-30 12:32:46,954 - modules.friend_request_window - INFO -    🆔 准考证: 014325110149
2025-07-30 12:32:46,955 - modules.friend_request_window - INFO -    👤 姓名: 钟建强
2025-07-30 12:32:46,956 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 12:32:46,956 - modules.friend_request_window - INFO -    📝 备注格式: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:32:46,956 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 12:32:46,957 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:32:46,957 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 12:32:46,959 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 13175710, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 12:32:46,962 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 13175710)
2025-07-30 12:32:46,963 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 12:32:46,964 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 12:32:46,964 - modules.friend_request_window - INFO - 🔄 激活窗口: 13175710
2025-07-30 12:32:47,668 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 12:32:47,668 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 12:32:47,669 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 12:32:47,669 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 12:32:47,670 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 12:32:47,670 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 12:32:47,671 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 12:32:47,671 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 12:32:47,671 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 12:32:47,672 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 12:32:47,672 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 12:32:47,673 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 12:32:47,673 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 12:32:47,674 - modules.friend_request_window - INFO -    📝 remark参数: '014325110149-钟建强-2025-07-30 20:32:46' (类型: <class 'str'>, 长度: 36)
2025-07-30 12:32:47,675 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 12:32:47,675 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:32:47,676 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 12:32:47,676 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 12:32:47,677 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 12:32:47,677 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 12:32:47,678 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 12:32:47,678 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 12:32:47,678 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 12:32:48,593 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 12:32:53,833 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 12:32:53,834 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 12:32:53,834 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 12:32:53,834 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 12:32:53,836 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-30 12:32:54,147 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:32:54,148 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:32:55,050 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:32:55,063 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:32:55,064 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 12:32:55,065 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 12:32:55,066 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 12:32:55,066 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 12:32:55,567 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 12:32:55,567 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 12:32:55,568 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 12:32:55,568 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 12:32:55,568 - modules.friend_request_window - INFO -    📝 内容: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:32:55,569 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 12:32:55,569 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110149-\xe9\x92\x9f\xe5\xbb\xba\xe5\xbc\xba-2025-07-30 20:32:46'
2025-07-30 12:32:55,569 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 12:32:56,475 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 12:33:01,717 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 12:33:01,718 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 12:33:01,718 - modules.friend_request_window - INFO -    📝 原始文本: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:33:01,718 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 12:33:01,719 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '<EMAIL>...' (前50字符)
2025-07-30 12:33:02,028 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 12:33:02,028 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 12:33:02,931 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 12:33:02,941 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 12:33:02,942 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:33:02,942 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 12:33:02,943 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:33:02,943 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 12:33:03,444 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110149-钟建强-2025-07-30 20:32:46'
2025-07-30 12:33:03,445 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 12:33:03,445 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 12:33:03,445 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 12:33:03,445 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 12:33:03,446 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 12:33:03,446 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 12:33:04,247 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 12:33:04,247 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 12:33:04,247 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 12:33:04,859 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 12:33:04,860 - modules.friend_request_window - INFO - 📋 当前黑名单中有 1 个窗口
2025-07-30 12:33:04,860 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 12:33:04,860 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 12:33:04,861 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 12:33:05,362 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 12:33:05,364 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 12:33:05,364 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 12:33:05,365 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 12:33:05,365 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 12:33:05,365 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 12:33:05,365 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 12:33:05,366 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 12:33:05,366 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 12:33:05,366 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 12:33:05,367 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 12:33:05,367 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 12:33:05,368 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 12:33:05,368 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 12:33:05,368 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 12:33:05,369 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 12:33:05,370 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 12:33:05,370 - modules.friend_request_window - INFO - 🚫 将检测到频率错误的窗口添加到黑名单...
2025-07-30 12:33:05,376 - modules.friend_request_window - INFO - ✅ 窗口已永久标记为不可用，防止重新激活
2025-07-30 12:33:05,377 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 12:33:05,377 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 12:33:05,880 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 12:33:05,881 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 12:33:05,881 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 12:33:05,882 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 12:33:05,882 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 12:33:05,882 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 12:33:05,882 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 12:33:05,883 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 12:33:06,792 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 12:33:06,792 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 12:33:06,792 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 12:33:06,793 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 12:33:06,809 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 12:33:06,810 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 12:33:07,610 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 12:33:07,611 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 12:33:07,611 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 12:33:07,611 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 12:33:07,612 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 12:33:07,612 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 12:33:07,613 - modules.friend_request_window - INFO - 🎯 使用固定坐标偏移(700, 16)关闭微信主窗口
2025-07-30 12:33:08,526 - modules.friend_request_window - INFO - ✅ 固定坐标偏移关闭微信主窗口成功
2025-07-30 12:33:08,527 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 12:33:08,527 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 12:33:08,528 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 12:33:10,528 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 12:33:10,530 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:33:10,530 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 12:33:10,530 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:10,532 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:10,532 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:10,534 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 12:33:10,534 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 12:33:10,535 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 12:33:10,556 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 12:33:10,559 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 12:33:10,559 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 12:33:10,560 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 263568
2025-07-30 12:33:10,562 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:33:10,562 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 263568) - 增强版
2025-07-30 12:33:10,562 - modules.window_manager - INFO - 🔄 窗口不可见，正在显示...
2025-07-30 12:33:11,085 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 12:33:11,086 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 12:33:11,086 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 12:33:11,086 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 12:33:11,087 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 12:33:11,087 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 12:33:11,088 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 12:33:11,088 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 12:33:11,088 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 12:33:11,088 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 12:33:11,291 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 12:33:11,292 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 12:33:11,292 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 12:33:11,292 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 12:33:11,292 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 12:33:11,293 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 12:33:11,293 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 12:33:13,294 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 12:33:13,295 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:33:13,295 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 12:33:13,296 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 12:33:13,296 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 12:33:13,296 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 12:33:13,296 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 12:33:13,297 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 12:33:13,297 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 12:33:13,297 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 12:33:13,298 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 12:33:13,298 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 12:33:13,499 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 12:33:13,499 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 12:33:15,874 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 12:33:15,874 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 12:33:15,875 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 12:33:18,006 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 12:33:18,216 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 12:33:18,219 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 12:33:20,606 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 12:33:20,606 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 12:33:20,606 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 12:33:22,671 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 12:33:22,872 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 12:33:22,872 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 12:33:25,256 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 12:33:25,258 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 12:33:25,258 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 12:33:28,205 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 12:33:28,406 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 12:33:28,407 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 12:33:30,774 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 12:33:30,775 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 12:33:30,776 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.8 秒...
2025-07-30 12:33:33,601 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 12:33:33,802 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 12:33:33,803 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 12:33:36,190 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 12:33:36,191 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 12:33:36,192 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:33:36,193 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 12:33:36,194 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 12:33:36,194 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:36,728 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:36,731 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:36,733 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:36,738 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:36,742 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:37,243 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:37,246 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:37,248 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:37,262 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:37,263 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:37,266 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:37,767 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:37,768 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:37,769 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:37,771 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:37,771 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:37,773 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:38,274 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:38,275 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:38,276 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:38,277 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:38,277 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:38,278 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:38,779 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:38,781 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:38,781 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:38,783 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:38,784 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:38,786 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:39,287 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:39,290 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:39,290 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:39,292 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:39,292 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:39,294 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:39,795 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:39,798 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:39,799 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:39,800 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:39,802 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:39,807 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:40,309 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:40,310 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:40,311 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:40,312 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:40,313 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:40,314 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:40,815 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:40,816 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:40,817 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:40,818 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:40,819 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:40,822 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:41,322 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:41,324 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:41,324 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:41,326 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:41,326 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:41,328 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:41,829 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:41,830 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:41,831 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:41,831 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:41,832 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:41,833 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:42,334 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:42,337 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:42,337 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:42,338 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:42,339 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:42,340 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:42,841 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:42,842 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:42,843 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:42,844 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:42,844 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:42,845 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:43,346 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:43,347 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:43,348 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:43,348 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:43,349 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:43,350 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:43,851 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 12:33:43,853 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:43,854 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 263568, 进程: Weixin.exe)
2025-07-30 12:33:43,855 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 12:33:43,855 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 9045874, 进程: Weixin.exe)
2025-07-30 12:33:43,857 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 12:33:44,358 - modules.window_manager - WARNING - ⚠️ 在8秒内未找到添加朋友窗口
2025-07-30 12:33:44,358 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未在指定时间内出现
2025-07-30 12:33:44,358 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未出现，但点击操作已执行
2025-07-30 12:33:44,359 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 12:33:44,359 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
