2025-07-30 14:56:21,829 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:56:21,830 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:56:21,832 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 14:56:21,833 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:56:21,834 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:56:21,835 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 14:56:21,836 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 14:56:21,838 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 14:56:21,838 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 14:56:21,838 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 14:56:21,839 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 14:56:21,839 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 14:56:21,839 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 14:56:21,842 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:56:21,846 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_145621.log
2025-07-30 14:56:21,849 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:56:21,851 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 14:56:21,858 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 14:56:21,859 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 14:56:21,859 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 14:56:21,860 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 14:56:21,862 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 22:56:21
2025-07-30 14:56:21,863 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 14:56:21,868 - __main__ - INFO - 📅 启动时间: 2025-07-30 22:56:21
2025-07-30 14:56:21,872 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 14:56:21,880 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:56:22,420 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:22,420 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:56:22,421 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:22,421 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1183578, 进程: Weixin.exe)
2025-07-30 14:56:22,948 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:22,949 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 14:56:22,950 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 14:56:22,952 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 14:56:22,953 - __main__ - INFO -   🔍 总发现窗口: 3
2025-07-30 14:56:22,953 - __main__ - INFO -   ✅ 有效可用窗口: 3
2025-07-30 14:56:22,953 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 14:56:22,954 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 14:56:22,956 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 14:56:22,956 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 14:56:22,957 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 1183578)
2025-07-30 14:56:22,960 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 6032430)
2025-07-30 14:56:22,962 - __main__ - INFO -   🔥 窗口 3: 微信 (句柄: 6621532)
2025-07-30 14:56:22,963 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 14:56:22,964 - __main__ - INFO - 📊 需要移动的窗口数量: 3
2025-07-30 14:56:22,965 - __main__ - INFO - 🔄 移动窗口 1/3: 微信 (句柄: 1183578)
2025-07-30 14:56:22,967 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 14:56:23,276 - __main__ - INFO - 🔄 移动窗口 2/3: 微信 (句柄: 6032430)
2025-07-30 14:56:23,279 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 14:56:23,280 - __main__ - WARNING - ⚠️ 窗口 2 移动失败
2025-07-30 14:56:23,580 - __main__ - INFO - 🔄 移动窗口 3/3: 微信 (句柄: 6621532)
2025-07-30 14:56:23,584 - __main__ - WARNING - ⚠️ SetWindowPos 调用失败
2025-07-30 14:56:23,584 - __main__ - WARNING - ⚠️ 窗口 3 移动失败
2025-07-30 14:56:23,584 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 14:56:23,585 - __main__ - INFO -   ✅ 成功移动: 1 个窗口
2025-07-30 14:56:23,585 - __main__ - INFO -   ❌ 移动失败: 2 个窗口
2025-07-30 14:56:23,585 - __main__ - INFO -   📈 成功率: 33.3%
2025-07-30 14:56:23,585 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 14:56:23,586 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 14:56:24,433 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 14:56:24,433 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 14:56:24,434 - __main__ - INFO - 📋 待处理联系人数: 2842
2025-07-30 14:56:24,435 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 14:56:24,435 - __main__ - INFO - 📊 总窗口数: 3, 总联系人数: 2842
2025-07-30 14:56:24,435 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 3
2025-07-30 14:56:24,436 - __main__ - INFO - 
============================================================
2025-07-30 14:56:24,437 - __main__ - INFO - 🎯 开始处理第 1/3 个微信窗口 (第 1 轮)
2025-07-30 14:56:24,437 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1183578)
2025-07-30 14:56:24,437 - __main__ - INFO - 📊 全局进度：已处理 0/2842 个联系人（剩余 2842 个）
2025-07-30 14:56:24,438 - __main__ - INFO - ============================================================
2025-07-30 14:56:24,438 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 14:56:24,438 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 1183578)
2025-07-30 14:56:24,439 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 14:56:24,441 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 14:56:24,441 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 1183578)
2025-07-30 14:56:24,442 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-07-30 14:56:24,443 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 14:56:25,443 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 14:56:26,545 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 14:56:26,546 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1183578
2025-07-30 14:56:26,546 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1183578) - 增强版
2025-07-30 14:56:26,851 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 14:56:26,851 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 14:56:26,852 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 14:56:26,852 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 14:56:26,852 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 14:56:26,853 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 14:56:26,853 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 14:56:26,853 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 14:56:26,854 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 14:56:26,854 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 14:56:27,056 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 14:56:27,057 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 14:56:27,060 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1183578 (API返回: None)
2025-07-30 14:56:27,363 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 14:56:27,363 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 14:56:27,364 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 14:56:28,364 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 14:56:28,365 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 14:56:28,365 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 14:56:28,365 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 14:56:28,366 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 14:56:28,366 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 14:56:28,367 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 14:56:28,367 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 14:56:28,367 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 14:56:28,568 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 14:56:28,569 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 14:56:28,569 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 14:56:30,945 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 14:56:30,945 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 14:56:30,946 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.9 秒...
2025-07-30 14:56:33,807 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 14:56:34,008 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 14:56:34,008 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 14:56:34,009 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 14:56:36,394 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 14:56:36,394 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 14:56:36,395 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 14:56:39,135 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 14:56:39,336 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 14:56:39,336 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 14:56:39,337 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 14:56:41,710 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 14:56:41,711 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 14:56:41,711 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 14:56:43,305 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 14:56:43,506 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 14:56:43,506 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 14:56:43,507 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 14:56:45,875 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 14:56:45,876 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 14:56:45,876 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 14:56:48,017 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 14:56:48,218 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 14:56:48,219 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 14:56:48,219 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 14:56:50,593 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 14:56:50,593 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 14:56:50,594 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 14:56:50,594 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 14:56:50,594 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:56:50,596 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:50,596 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1183578, 进程: Weixin.exe)
2025-07-30 14:56:50,597 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:50,597 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:56:50,598 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3739014, 进程: Weixin.exe)
2025-07-30 14:56:50,599 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:50,599 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 14:56:50,602 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 14:56:50,603 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 3739014)
2025-07-30 14:56:50,605 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3739014) - 增强版
2025-07-30 14:56:50,909 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 14:56:50,909 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 14:56:50,909 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 14:56:50,910 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 14:56:50,910 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 14:56:50,910 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 14:56:51,116 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 14:56:51,116 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 14:56:51,319 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 14:56:51,319 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 14:56:51,319 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 14:56:51,320 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 14:56:51,320 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 14:56:51,320 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 14:56:51,320 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 14:56:52,321 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 14:56:52,321 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:56:52,324 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:52,324 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 1183578, 进程: Weixin.exe)
2025-07-30 14:56:52,325 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:52,326 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:56:52,327 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 3739014, 进程: Weixin.exe)
2025-07-30 14:56:52,328 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:56:52,328 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 14:56:52,330 - modules.window_manager - INFO - 🎯 总共找到 4 个微信窗口
2025-07-30 14:56:52,331 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 14:56:52,331 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 14:56:52,332 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 14:56:52,333 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 14:56:52,333 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 1183578
2025-07-30 14:56:52,333 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 1183578) - 增强版
2025-07-30 14:56:52,642 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 14:56:52,642 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 14:56:52,643 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 14:56:52,643 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 14:56:52,643 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 14:56:52,643 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 14:56:52,644 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 14:56:52,644 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 14:56:52,644 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 14:56:52,645 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 14:56:52,846 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 14:56:52,847 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 14:56:52,848 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 1183578 (API返回: None)
2025-07-30 14:56:53,149 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 14:56:53,150 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 14:56:53,150 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 14:56:53,150 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 14:56:54,151 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 14:56:54,151 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 14:56:54,152 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 14:56:54,155 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_145654.log
2025-07-30 14:56:54,157 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:56:54,158 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 14:56:54,158 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 14:56:54,159 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 14:56:54,159 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 14:56:54,159 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 14:56:54,161 - modules.wechat_auto_add_simple - INFO - 🎯 找到 4 个微信窗口:
2025-07-30 14:56:54,162 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 14:56:54,162 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 3 个
2025-07-30 14:56:54,163 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 14:56:54,163 - modules.wechat_auto_add_simple - INFO -   2. 微信 (726x650) - main
2025-07-30 14:56:54,164 - modules.wechat_auto_add_simple - INFO -   3. 添加朋友 (328x454) - add_friend
2025-07-30 14:56:54,164 - modules.wechat_auto_add_simple - INFO -   4. 微信 (726x650) - main
2025-07-30 14:56:54,164 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 14:56:54,165 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:56:54,166 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 3739014
2025-07-30 14:56:54,166 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 3739014) - 增强版
2025-07-30 14:56:54,474 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 14:56:54,474 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 14:56:54,475 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 14:56:54,475 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 14:56:54,475 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 14:56:54,476 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 14:56:54,476 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 14:56:54,476 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 14:56:54,678 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 14:56:54,679 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 14:56:54,682 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 3739014 (API返回: None)
2025-07-30 14:56:54,983 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 14:56:54,984 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 14:56:54,984 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 14:56:54,984 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 14:56:54,985 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:56:54,985 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 14:56:54,986 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 14:56:54,989 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 14:56:54,990 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 14:56:55,476 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 14:56:55,476 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 14:56:55,734 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2842 个
2025-07-30 14:56:55,735 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2842 个 (总计: 3135 个)
2025-07-30 14:56:55,736 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 14:56:55,736 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 14:56:55,736 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:56:55,737 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:56:55,738 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 14:56:55,739 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 14:56:55,739 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2842
2025-07-30 14:56:55,739 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 15169010906 (张薇)
2025-07-30 14:56:55,740 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:56:55,740 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:56:55,742 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 14:57:02,317 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 15169010906
2025-07-30 14:57:02,318 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 14:57:02,318 - modules.wechat_auto_add_simple - INFO - 👥 开始为 15169010906 执行添加朋友操作...
2025-07-30 14:57:02,318 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 14:57:02,319 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 14:57:02,319 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 14:57:02,321 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 14:57:02,324 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 3 个图片文件
2025-07-30 14:57:02,325 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 14:57:02,326 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 14:57:02,327 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 14:57:02,327 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 14:57:02,328 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 14:57:02,328 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 14:57:02,328 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 14:57:02,334 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 14:57:02,339 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 14:57:02,342 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 14:57:02,346 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 14:57:02,348 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 14:57:02,350 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-07-30 14:57:02,352 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 14:57:02,853 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 14:57:02,854 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 14:57:02,932 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差39.95, 边缘比例0.0402
2025-07-30 14:57:02,939 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_145702.png
2025-07-30 14:57:02,941 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 14:57:02,942 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 14:57:02,943 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 14:57:02,948 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 14:57:02,949 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 14:57:02,955 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_145702.png
2025-07-30 14:57:02,957 - WeChatAutoAdd - INFO - 底部区域原始检测到 5 个轮廓
2025-07-30 14:57:02,959 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,452), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 14:57:02,960 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,452), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 14:57:02,962 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 14:57:02,964 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 14:57:02,965 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,209), 尺寸2x239, 长宽比0.01, 面积478
2025-07-30 14:57:02,966 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸326x245, 长宽比1.33, 面积79870
2025-07-30 14:57:02,967 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 14:57:02,967 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 14:57:02,969 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 14:57:02,970 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 14:57:02,979 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_145702.png
2025-07-30 14:57:02,982 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 14:57:02,983 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 14:57:02,988 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_145702.png
2025-07-30 14:57:03,047 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 14:57:03,048 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 14:57:03,049 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 14:57:03,050 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 14:57:03,351 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 14:57:04,121 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 14:57:04,123 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 14:57:04,124 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:57:04,124 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:57:04,126 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 14:57:04,127 - modules.wechat_auto_add_simple - INFO - ✅ 15169010906 添加朋友操作执行成功
2025-07-30 14:57:04,128 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:57:04,129 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:57:04,130 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 14:57:04,131 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 14:57:06,132 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 14:57:06,133 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 14:57:06,133 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 14:57:06,133 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 14:57:06,133 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 14:57:06,134 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 14:57:06,134 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 14:57:06,134 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 14:57:06,135 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 14:57:06,135 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 15169010906
2025-07-30 14:57:06,139 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 14:57:06,140 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 14:57:06,140 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 14:57:06,141 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 14:57:06,142 - modules.friend_request_window - INFO -    📱 phone: '15169010906'
2025-07-30 14:57:06,142 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 14:57:06,142 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 14:57:06,642 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 14:57:06,642 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 14:57:06,643 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 14:57:06,643 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 14:57:06,644 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 15169010906
2025-07-30 14:57:06,645 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 14:57:06,645 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 14:57:06,647 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 14:57:06,647 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 14:57:06,647 - modules.friend_request_window - INFO -    📱 手机号码: 15169010906
2025-07-30 14:57:06,647 - modules.friend_request_window - INFO -    🆔 准考证: 014325110428
2025-07-30 14:57:06,648 - modules.friend_request_window - INFO -    👤 姓名: 张薇
2025-07-30 14:57:06,648 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 14:57:06,649 - modules.friend_request_window - INFO -    📝 备注格式: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:06,650 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 14:57:06,650 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:06,651 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 14:57:06,653 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 2297300, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 14:57:06,659 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 2297300)
2025-07-30 14:57:06,660 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 14:57:06,661 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 14:57:06,661 - modules.friend_request_window - INFO - 🔄 激活窗口: 2297300
2025-07-30 14:57:07,365 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 14:57:07,366 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 14:57:07,366 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 14:57:07,367 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 14:57:07,367 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 14:57:07,367 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 14:57:07,368 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 14:57:07,369 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 14:57:07,370 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 14:57:07,370 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 14:57:07,374 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 14:57:07,375 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 14:57:07,375 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 14:57:07,375 - modules.friend_request_window - INFO -    📝 remark参数: '014325110428-张薇-2025-07-30 22:57:06' (类型: <class 'str'>, 长度: 35)
2025-07-30 14:57:07,376 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 14:57:07,376 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:07,376 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 14:57:07,377 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 14:57:07,377 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 14:57:07,377 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 14:57:07,377 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 14:57:07,378 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 14:57:07,378 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 14:57:08,291 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 14:57:13,533 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 14:57:13,533 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 14:57:13,533 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 14:57:13,534 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 14:57:13,535 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-30 14:49:52,694 - modules.friend_request_wi...' (前50字符)
2025-07-30 14:57:13,847 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 14:57:13,848 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 14:57:14,750 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 14:57:14,760 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 14:57:14,762 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 14:57:14,762 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 14:57:14,762 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 14:57:14,763 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 14:57:15,264 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 14:57:15,264 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 14:57:15,264 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 14:57:15,265 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 14:57:15,265 - modules.friend_request_window - INFO -    📝 内容: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:15,265 - modules.friend_request_window - INFO -    📏 内容长度: 35 字符
2025-07-30 14:57:15,265 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325110428-\xe5\xbc\xa0\xe8\x96\x87-2025-07-30 22:57:06'
2025-07-30 14:57:15,266 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 14:57:16,174 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 14:57:21,418 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 14:57:21,418 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 14:57:21,418 - modules.friend_request_window - INFO -    📝 原始文本: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:21,419 - modules.friend_request_window - INFO -    📏 文本长度: 35 字符
2025-07-30 14:57:21,419 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-30 14:49:52,694 - modules.friend_request_wi...' (前50字符)
2025-07-30 14:57:21,732 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 14:57:21,732 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 14:57:22,635 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 14:57:22,645 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 14:57:22,646 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:22,646 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 14:57:22,647 - modules.friend_request_window - INFO -    📝 已填写内容: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:22,647 - modules.friend_request_window - INFO -    � 内容长度: 35 字符
2025-07-30 14:57:23,148 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:57:23,148 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 14:57:23,149 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 14:57:23,149 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 14:57:23,149 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 14:57:23,149 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 14:57:23,150 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 14:57:23,950 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 14:57:23,951 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 14:57:23,951 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 14:57:24,558 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:57:24,559 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:57:24,562 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 3 个微信窗口
2025-07-30 14:57:24,562 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 14:57:24,563 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 14:57:24,563 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 14:57:25,065 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 14:57:25,067 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 14:57:25,067 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 14:57:25,068 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 14:57:25,068 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 14:57:25,068 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 14:57:25,068 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 14:57:25,069 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 14:57:25,069 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 14:57:25,069 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 14:57:25,069 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 14:57:25,070 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 14:57:25,071 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 14:57:25,072 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 14:57:25,072 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 14:57:25,072 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 14:57:25,073 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 14:57:25,075 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 4
2025-07-30 14:57:25,076 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 14:57:25,078 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 1771268)
2025-07-30 14:57:25,079 - modules.friend_request_window - INFO - 🔍 验证窗口 1771268 是否已正确关闭...
2025-07-30 14:57:26,080 - modules.friend_request_window - WARNING - ⚠️ 窗口 1771268 仍然存在且可见，未正确关闭
2025-07-30 14:57:26,080 - modules.friend_request_window - WARNING - ⚠️ 窗口未能正确关闭，暂不添加到黑名单
2025-07-30 14:57:26,080 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 14:57:26,081 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 14:57:26,583 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 14:57:26,584 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 14:57:26,584 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 14:57:26,584 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 14:57:26,584 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 14:57:26,585 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 14:57:26,585 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 14:57:26,585 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 14:57:27,494 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 14:57:27,494 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 14:57:27,495 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 14:57:27,495 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 14:57:27,511 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 14:57:27,512 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 14:57:28,313 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 14:57:28,313 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 14:57:28,313 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 14:57:28,314 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 14:57:28,314 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 14:57:28,314 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 14:57:28,315 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 14:57:28,315 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 14:57:29,816 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 14:57:29,816 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 14:57:29,817 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 14:57:29,817 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 14:57:31,818 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 14:57:31,819 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:57:31,819 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 14:57:31,820 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:57:31,821 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:57:31,821 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:57:31,822 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:57:31,823 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 14:57:31,825 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 14:57:31,826 - modules.friend_request_window - INFO - 🔄 使用window_manager切换到下一个微信窗口...
2025-07-30 14:57:31,827 - modules.window_manager - INFO - 📍 当前窗口: 微信 (句柄: 6032430)
2025-07-30 14:57:31,828 - modules.window_manager - INFO - 🔄 切换到微信窗口 2/2: 微信
2025-07-30 14:57:31,829 - modules.window_manager - INFO - 🎯 目标窗口详情: 句柄=6621532, 类名=Qt51514QWindowIcon
2025-07-30 14:57:31,830 - modules.window_manager - INFO - 🚀 开始智能窗口切换: 微信 (句柄: 6621532)
2025-07-30 14:57:31,832 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 14:57:32,439 - modules.window_manager - INFO - ✅ 增强激活策略成功
2025-07-30 14:57:32,740 - modules.window_manager - INFO - ✅ 窗口已成功切换到前台
2025-07-30 14:57:32,740 - modules.window_manager - INFO - ✅ 窗口切换验证成功
2025-07-30 14:57:32,741 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 14:57:32,741 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 14:57:32,741 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 14:57:32,742 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 14:57:32,742 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 14:57:32,742 - modules.window_manager - INFO - 📅 安排延迟新闻提示框检测: 微信 (窗口 2)
2025-07-30 14:57:32,743 - modules.window_manager - INFO - ✅ 窗口切换完成: 微信
2025-07-30 14:57:32,744 - modules.window_manager - INFO - ✅ 窗口切换成功，已验证窗口状态
2025-07-30 14:57:32,744 - modules.friend_request_window - INFO - ✅ 成功切换到微信窗口: 微信
2025-07-30 14:57:32,744 - modules.friend_request_window - INFO -    窗口句柄: 6621532
2025-07-30 14:57:32,744 - modules.friend_request_window - INFO -    窗口类名: Qt51514QWindowIcon
2025-07-30 14:57:32,744 - modules.friend_request_window - INFO - 🔧 窗口切换完成，检查添加朋友窗口位置同步...
2025-07-30 14:57:33,744 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 14:57:33,745 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 14:57:33,748 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 14:57:34,249 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 14:57:34,748 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 14:57:34,749 - modules.friend_request_window - WARNING - ⚠️ 未找到添加朋友窗口，尝试等待窗口出现...
2025-07-30 14:57:36,252 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 14:57:36,256 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 14:57:36,752 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 14:57:36,752 - modules.friend_request_window - ERROR - ❌ 仍未找到添加朋友窗口，位置同步失败
2025-07-30 14:57:36,752 - modules.friend_request_window - WARNING - ⚠️ 窗口切换后添加朋友窗口位置同步失败，但继续执行
2025-07-30 14:57:36,753 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 14:57:36,753 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 14:57:36,753 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 14:57:36,756 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 14:57:38,754 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 14:57:38,755 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:57:38,755 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 14:57:38,755 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 14:57:38,756 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 14:57:38,756 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 14:57:38,756 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 14:57:38,757 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 14:57:38,757 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 14:57:38,757 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 14:57:38,757 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 14:57:38,758 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 14:57:38,959 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 14:57:38,959 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 14:57:38,959 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 14:57:39,758 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 14:57:39,762 - modules.window_manager - INFO - 🎯 微信窗口 2 额外新闻提示框检测
2025-07-30 14:57:40,262 - modules.wechat_news_popup_blocker.WeChatNewsPopupBlocker - INFO - ✅ 微信新闻提示框阻止器初始化完成
2025-07-30 14:57:41,341 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 14:57:41,342 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 14:57:41,342 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 14:57:44,046 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 14:57:44,247 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 14:57:44,248 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 14:57:44,248 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 14:57:46,623 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 14:57:46,623 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 14:57:46,623 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 14:57:49,110 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 14:57:49,312 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 14:57:49,312 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 14:57:49,313 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 14:57:51,691 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 14:57:51,691 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 14:57:51,691 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 14:57:54,040 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 14:57:54,241 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 14:57:54,242 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 14:57:54,242 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 14:57:56,622 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 14:57:56,622 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 14:57:56,622 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 14:57:59,018 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 14:57:59,219 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 14:57:59,220 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 14:57:59,220 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 14:58:01,607 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 14:58:01,608 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 14:58:01,608 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 14:58:01,609 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:58:01,610 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 14:58:01,610 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:58:01,612 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:58:01,612 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:58:01,613 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2295098, 进程: Weixin.exe)
2025-07-30 14:58:01,614 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:58:01,615 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 14:58:01,619 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 14:58:01,621 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 2295098)
2025-07-30 14:58:01,622 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2295098) - 增强版
2025-07-30 14:58:01,926 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 14:58:01,926 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 14:58:01,926 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 14:58:01,927 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 14:58:01,927 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 14:58:01,927 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 14:58:02,131 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 14:58:02,131 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 14:58:02,333 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 14:58:02,333 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 14:58:02,334 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 14:58:02,334 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 14:58:02,335 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 14:58:02,335 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 14:58:02,335 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 14:58:03,336 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 14:58:03,337 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:58:03,337 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:58:03,339 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:58:03,340 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:58:03,341 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 2295098, 进程: Weixin.exe)
2025-07-30 14:58:03,341 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:58:03,342 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6621532, 进程: Weixin.exe)
2025-07-30 14:58:03,344 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-07-30 14:58:03,344 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 14:58:03,345 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 14:58:03,345 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 14:58:03,345 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 14:58:03,345 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 14:58:03,346 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 14:58:04,349 - modules.friend_request_window - INFO - 🔍 共找到 1 个添加朋友窗口
2025-07-30 14:58:04,349 - modules.friend_request_window - INFO - 📍 处理添加朋友窗口: 添加朋友
2025-07-30 14:58:04,350 - modules.friend_request_window - INFO -    当前位置: (0, 0)
2025-07-30 14:58:04,350 - modules.friend_request_window - INFO -    窗口大小: 328x454
2025-07-30 14:58:04,350 - modules.friend_request_window - INFO -    目标位置: (1200, 0)
2025-07-30 14:58:04,350 - modules.friend_request_window - INFO - 🔧 开始移动窗口 2295098 到目标位置...
2025-07-30 14:58:05,155 - modules.friend_request_window - INFO - ✅ 方法1成功: 窗口移动到位置 (1200, 0)
2025-07-30 14:58:05,155 - modules.friend_request_window - INFO - 📊 位置同步统计: 1/1 个窗口同步成功
2025-07-30 14:58:05,155 - modules.friend_request_window - INFO - ✅ 添加朋友窗口位置同步成功
2025-07-30 14:58:05,156 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 14:58:05,156 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 14:58:05,157 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 14:58:05,157 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 14:58:05,157 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 14:58:05,157 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 14:58:05,158 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 14:58:05,158 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 14:58:05,158 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 14:58:05,159 - modules.friend_request_window - INFO -    📝 备注信息: '014325110428-张薇-2025-07-30 22:57:06'
2025-07-30 14:58:05,659 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 14:58:05,660 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:58:05,661 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:58:05,662 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 14:58:05,663 - modules.wechat_auto_add_simple - INFO - ✅ 15169010906 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 14:58:05,663 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 15169010906
2025-07-30 14:58:05,664 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:58:05,665 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:58:05,666 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 14:58:09,267 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2842
2025-07-30 14:58:09,267 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 18513345287 (宋刘爱迪)
2025-07-30 14:58:09,268 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:58:09,268 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:58:09,272 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 14:58:15,847 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 18513345287
2025-07-30 14:58:15,847 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 14:58:15,847 - modules.wechat_auto_add_simple - INFO - 👥 开始为 18513345287 执行添加朋友操作...
2025-07-30 14:58:15,848 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 14:58:15,848 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 14:58:15,849 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 14:58:15,850 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 14:58:15,854 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 14:58:15,856 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 14:58:15,856 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 14:58:15,857 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 14:58:15,857 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 14:58:15,857 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 14:58:15,859 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 14:58:15,860 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 14:58:15,866 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 14:58:15,868 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 14:58:15,870 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 14:58:15,871 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 14:58:15,874 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-07-30 14:58:15,881 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 14:58:16,384 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 14:58:16,385 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 14:58:16,449 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-07-30 14:58:16,451 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-07-30 14:58:16,459 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_145816.png
2025-07-30 14:58:16,462 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 14:58:16,464 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 14:58:16,466 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 14:58:16,468 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 14:58:16,470 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 14:58:16,477 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_145816.png
2025-07-30 14:58:16,478 - WeChatAutoAdd - INFO - 底部区域原始检测到 5 个轮廓
2025-07-30 14:58:16,480 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,452), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 14:58:16,481 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,452), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 14:58:16,482 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-07-30 14:58:16,483 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-07-30 14:58:16,485 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(326,209), 尺寸2x239, 长宽比0.01, 面积478
2025-07-30 14:58:16,490 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸326x245, 长宽比1.33, 面积79870
2025-07-30 14:58:16,491 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-07-30 14:58:16,493 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-07-30 14:58:16,494 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 14:58:16,496 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 14:58:16,504 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_145816.png
2025-07-30 14:58:16,506 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 14:58:16,508 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-07-30 14:58:16,512 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_145816.png
2025-07-30 14:58:16,538 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 14:58:16,544 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-07-30 14:58:16,546 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 14:58:16,548 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 14:58:16,849 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-07-30 14:58:17,620 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 14:58:17,621 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 14:58:17,623 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:58:17,623 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:58:17,625 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 14:58:17,626 - modules.wechat_auto_add_simple - INFO - ✅ 18513345287 添加朋友操作执行成功
2025-07-30 14:58:17,626 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:58:17,627 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:58:17,630 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 14:58:17,630 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 14:58:19,632 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 申请添加朋友
2025-07-30 14:58:19,632 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 14:58:19,633 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 14:58:19,633 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 14:58:19,634 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 14:58:19,634 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 14:58:19,634 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 14:58:19,635 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 14:58:19,635 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 14:58:19,636 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 18513345287
2025-07-30 14:58:19,636 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 14:58:19,637 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 14:58:19,638 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 14:58:19,639 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 14:58:19,640 - modules.friend_request_window - INFO -    📱 phone: '18513345287'
2025-07-30 14:58:19,643 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 14:58:19,645 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 14:58:20,203 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 14:58:20,204 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 14:58:20,204 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 14:58:20,205 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 14:58:20,206 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 18513345287
2025-07-30 14:58:20,206 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 14:58:20,207 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 14:58:20,207 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 14:58:20,208 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 14:58:20,208 - modules.friend_request_window - INFO -    📱 手机号码: 18513345287
2025-07-30 14:58:20,208 - modules.friend_request_window - INFO -    🆔 准考证: 014325120001
2025-07-30 14:58:20,208 - modules.friend_request_window - INFO -    👤 姓名: 宋刘爱迪
2025-07-30 14:58:20,209 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 14:58:20,210 - modules.friend_request_window - INFO -    📝 备注格式: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:20,210 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 14:58:20,210 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:20,211 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 14:58:20,212 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 3870086, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 14:58:20,213 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 3870086)
2025-07-30 14:58:20,214 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 14:58:20,214 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 14:58:20,215 - modules.friend_request_window - INFO - 🔄 激活窗口: 3870086
2025-07-30 14:58:20,918 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 14:58:20,919 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 14:58:20,919 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 14:58:20,919 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 14:58:20,920 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 14:58:20,920 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 14:58:20,920 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 14:58:20,920 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 14:58:20,921 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 14:58:20,921 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 14:58:20,921 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 14:58:20,921 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 14:58:20,922 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 14:58:20,922 - modules.friend_request_window - INFO -    📝 remark参数: '014325120001-宋刘爱迪-2025-07-30 22:58:20' (类型: <class 'str'>, 长度: 37)
2025-07-30 14:58:20,923 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 14:58:20,923 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:20,923 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 14:58:20,923 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 14:58:20,924 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 14:58:20,924 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 14:58:20,925 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 14:58:20,925 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 14:58:20,925 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 14:58:21,838 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 14:58:27,085 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 14:58:27,085 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 14:58:27,085 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 14:58:27,086 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 14:58:27,086 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-30 14:49:52,694 - modules.friend_request_wi...' (前50字符)
2025-07-30 14:58:27,396 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 14:58:27,396 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 14:58:28,299 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 14:58:28,309 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 14:58:28,309 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 14:58:28,310 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 14:58:28,311 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 14:58:28,312 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 14:58:28,812 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 14:58:28,813 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 14:58:28,813 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 14:58:28,813 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 14:58:28,813 - modules.friend_request_window - INFO -    📝 内容: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:28,814 - modules.friend_request_window - INFO -    📏 内容长度: 37 字符
2025-07-30 14:58:28,814 - modules.friend_request_window - INFO -    🔤 内容编码: b'014325120001-\xe5\xae\x8b\xe5\x88\x98\xe7\x88\xb1\xe8\xbf\xaa-2025-07-30 22:58:20'
2025-07-30 14:58:28,814 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 14:58:29,722 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 14:58:34,971 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 14:58:34,972 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 14:58:34,972 - modules.friend_request_window - INFO -    📝 原始文本: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:34,973 - modules.friend_request_window - INFO -    📏 文本长度: 37 字符
2025-07-30 14:58:34,973 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '025-07-30 14:49:52,694 - modules.friend_request_wi...' (前50字符)
2025-07-30 14:58:35,284 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 14:58:35,284 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 14:58:36,187 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 14:58:36,200 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 14:58:36,200 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:36,201 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 14:58:36,201 - modules.friend_request_window - INFO -    📝 已填写内容: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:36,203 - modules.friend_request_window - INFO -    � 内容长度: 37 字符
2025-07-30 14:58:36,703 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:58:36,704 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 14:58:36,704 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 14:58:36,705 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 14:58:36,705 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 14:58:36,705 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 14:58:36,706 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 14:58:37,506 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 14:58:37,507 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 14:58:37,507 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 14:58:38,118 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:58:38,119 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:58:38,121 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-07-30 14:58:38,121 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 14:58:38,122 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 14:58:38,122 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 14:58:38,624 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 14:58:38,626 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 14:58:38,626 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 14:58:38,626 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 14:58:38,626 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 14:58:38,627 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 14:58:38,627 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 14:58:38,627 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 14:58:38,627 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 14:58:38,628 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 14:58:38,628 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 14:58:38,628 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 14:58:38,629 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 14:58:38,629 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 14:58:38,629 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 14:58:38,630 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 14:58:38,631 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 14:58:38,636 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 3
2025-07-30 14:58:38,636 - modules.friend_request_window - INFO - 🚫 准备将检测到频率错误的窗口添加到黑名单...
2025-07-30 14:58:38,637 - modules.friend_request_window - INFO - 📋 窗口信息: Weixin (句柄: 5572494)
2025-07-30 14:58:38,637 - modules.friend_request_window - INFO - 🔍 验证窗口 5572494 是否已正确关闭...
2025-07-30 14:58:39,638 - modules.friend_request_window - WARNING - ⚠️ 窗口 5572494 仍然存在且可见，未正确关闭
2025-07-30 14:58:39,638 - modules.friend_request_window - WARNING - ⚠️ 窗口未能正确关闭，暂不添加到黑名单
2025-07-30 14:58:39,639 - modules.friend_request_window - INFO - 🖱️ 准备点击错误窗口确定按钮: Weixin
2025-07-30 14:58:39,639 - modules.friend_request_window - INFO - 📊 窗口大小: 330x194
2025-07-30 14:58:40,142 - modules.friend_request_window - INFO - 🖥️ 当前屏幕分辨率: 1920x1080
2025-07-30 14:58:40,142 - modules.friend_request_window - INFO - ✅ 使用预设坐标 (1920x1080): (1364, 252)
2025-07-30 14:58:40,143 - modules.friend_request_window - INFO - 📍 使用适配坐标 (错误提示窗口): (1364, 252)
2025-07-30 14:58:40,143 - modules.friend_request_window - INFO - 🎯 窗口信息: Weixin (330x194)
2025-07-30 14:58:40,143 - modules.friend_request_window - INFO - 📍 窗口位置: (1199, 130) 到 (1529, 324)
2025-07-30 14:58:40,143 - modules.friend_request_window - INFO - 🎯 开始增强点击操作，目标坐标: (1364, 252)
2025-07-30 14:58:40,144 - modules.friend_request_window - INFO - 📊 点击前窗口状态: 存在且可见
2025-07-30 14:58:40,144 - modules.friend_request_window - INFO - 🖱️ 方法1: pyautogui单击...
2025-07-30 14:58:41,053 - modules.friend_request_window - INFO - ✅ 验证成功：错误提示窗口已关闭
2025-07-30 14:58:41,054 - modules.friend_request_window - INFO - ✅ 方法1成功：pyautogui单击
2025-07-30 14:58:41,054 - modules.friend_request_window - INFO - ✅ 成功点击错误提示确定按钮
2025-07-30 14:58:41,054 - modules.friend_request_window - INFO - 🔄 开始关闭添加朋友窗口...
2025-07-30 14:58:41,076 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 添加朋友 (Qt51514QWindowIcon)
2025-07-30 14:58:41,077 - modules.friend_request_window - INFO -    方法1: 发送WM_CLOSE消息...
2025-07-30 14:58:41,877 - modules.friend_request_window - INFO -    ✅ WM_CLOSE成功关闭窗口
2025-07-30 14:58:41,878 - modules.friend_request_window - INFO - ✅ 已关闭添加朋友窗口: 添加朋友
2025-07-30 14:58:41,878 - modules.friend_request_window - INFO - 📊 关闭添加朋友窗口统计: 1/1
2025-07-30 14:58:41,878 - modules.friend_request_window - INFO - 🔄 开始关闭当前微信主窗口...
2025-07-30 14:58:41,879 - modules.friend_request_window - INFO - 🔄 尝试关闭窗口: 微信 (Qt51514QWindowIcon)
2025-07-30 14:58:41,879 - modules.friend_request_window - INFO - 🏠 检测到微信主窗口，使用固定坐标点击关闭按钮...
2025-07-30 14:58:41,880 - modules.friend_request_window - INFO - 🎯 使用优化方法关闭微信主窗口
2025-07-30 14:58:41,880 - modules.friend_request_window - INFO -    方法1: 使用WM_CLOSE消息关闭窗口...
2025-07-30 14:58:43,381 - modules.friend_request_window - INFO - ✅ WM_CLOSE消息成功关闭微信主窗口
2025-07-30 14:58:43,381 - modules.friend_request_window - INFO - ✅ 固定坐标关闭微信主窗口成功
2025-07-30 14:58:43,382 - modules.friend_request_window - INFO - ✅ 已关闭当前微信窗口: 微信
2025-07-30 14:58:43,382 - modules.friend_request_window - INFO - 🔄 开始切换到微信2窗口...
2025-07-30 14:58:45,383 - modules.friend_request_window - INFO - 📦 使用WeChatWindowManager进行窗口切换
2025-07-30 14:58:45,384 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:58:45,384 - modules.friend_request_window - INFO - 🔍 查找所有微信窗口...
2025-07-30 14:58:45,385 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:58:45,386 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:58:45,386 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:58:45,389 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:58:45,389 - modules.friend_request_window - WARNING - ⚠️ 只找到一个或没有微信窗口，尝试使用备用方法
2025-07-30 14:58:45,389 - modules.friend_request_window - INFO - 🔄 使用增强备用方法切换到微信2窗口...
2025-07-30 14:58:45,412 - modules.friend_request_window - INFO - 🔍 找到 1 个微信2候选窗口
2025-07-30 14:58:45,412 - modules.friend_request_window - INFO -    1. 微信 (Qt51514QWindowIcon)
2025-07-30 14:58:45,413 - modules.friend_request_window - INFO - 🎯 找到微信2窗口: 微信
2025-07-30 14:58:45,413 - modules.friend_request_window - INFO - 🎯 增强激活窗口: 6032430
2025-07-30 14:58:45,414 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:58:45,414 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 14:58:45,735 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 14:58:45,736 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 14:58:45,737 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 14:58:45,737 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 14:58:45,738 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 14:58:45,739 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 14:58:45,739 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 14:58:45,740 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 14:58:45,740 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 14:58:45,740 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 14:58:45,942 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 14:58:45,942 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 14:58:45,942 - modules.friend_request_window - INFO - ✅ 使用window_manager激活成功
2025-07-30 14:58:45,943 - modules.friend_request_window - INFO - ✅ 增强备用方法成功切换到微信2窗口
2025-07-30 14:58:45,943 - modules.friend_request_window - INFO - 🚀 开始执行切换窗口后的主界面操作流程...
2025-07-30 14:58:45,943 - modules.friend_request_window - INFO - 📋 目标：在新的微信窗口中执行主界面点击操作
2025-07-30 14:58:45,943 - modules.friend_request_window - INFO - 💡 这是确保正确工作流程的关键步骤，不能跳过
2025-07-30 14:58:47,944 - modules.friend_request_window - INFO - 📦 成功导入主界面操作模块
2025-07-30 14:58:47,945 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:58:47,946 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 14:58:47,946 - modules.friend_request_window - INFO - 🔧 主界面操作模块初始化成功
2025-07-30 14:58:47,946 - modules.friend_request_window - INFO - 🎯 开始执行主界面操作流程...
2025-07-30 14:58:47,947 - modules.friend_request_window - INFO - 📋 将执行：微信按钮 -> 通讯录 -> 微信主按钮 -> +快捷操作 -> 添加朋友
2025-07-30 14:58:47,947 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 14:58:47,947 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 14:58:47,947 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 14:58:47,948 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 14:58:47,948 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 14:58:47,948 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 14:58:48,149 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 14:58:48,150 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 14:58:48,150 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 14:58:50,537 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 14:58:50,537 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 14:58:50,538 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 14:58:52,814 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 14:58:53,015 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 14:58:53,015 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 14:58:53,016 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 14:58:55,384 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 14:58:55,384 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 14:58:55,384 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 14:58:57,221 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 14:58:57,422 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 14:58:57,422 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 14:58:57,422 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 14:58:59,802 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 14:58:59,803 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 14:58:59,803 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.3 秒...
2025-07-30 14:59:02,077 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 14:59:02,279 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 14:59:02,279 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 14:59:02,280 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 14:59:04,652 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 14:59:04,653 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 14:59:04,653 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.7 秒...
2025-07-30 14:59:07,335 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 14:59:07,536 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 14:59:07,539 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 14:59:07,540 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 14:59:09,936 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 14:59:09,973 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 14:59:09,975 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 14:59:09,977 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:59:09,978 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 14:59:09,979 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:09,985 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:09,986 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:09,989 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:10,490 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:10,492 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:10,492 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:10,494 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:10,995 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:10,996 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:10,997 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:11,000 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:11,501 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:11,502 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:11,503 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:11,504 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:12,005 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:12,006 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:12,007 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:12,008 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:12,509 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:12,511 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:12,511 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:12,515 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:13,016 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:13,018 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:13,018 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:13,020 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:13,521 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:13,523 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:13,524 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:13,526 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:14,027 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:14,028 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:14,029 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:14,031 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:14,534 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:14,538 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:14,539 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:14,546 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:15,052 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:15,055 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:15,055 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:15,058 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:15,558 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:15,559 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:15,560 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:15,561 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:16,120 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:16,159 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:16,161 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:16,171 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:16,672 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:16,674 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:16,674 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:16,677 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:17,177 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:17,179 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:17,179 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:17,181 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:17,682 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:17,684 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:17,684 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:17,688 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:18,188 - modules.window_manager - WARNING - ⚠️ 在8秒内未找到添加朋友窗口
2025-07-30 14:59:18,189 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未在指定时间内出现
2025-07-30 14:59:18,189 - modules.main_interface - WARNING - ⚠️ 添加朋友窗口未出现，但点击操作已执行
2025-07-30 14:59:18,189 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 14:59:18,190 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 14:59:19,190 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 14:59:19,191 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 14:59:19,192 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 14:59:19,193 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 14:59:19,194 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 6032430, 进程: Weixin.exe)
2025-07-30 14:59:19,196 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 14:59:19,196 - modules.main_interface - WARNING - ⚠️ 未找到添加朋友窗口
2025-07-30 14:59:19,197 - modules.main_interface - WARNING - ⚠️ [主界面操作] 添加朋友窗口验证失败，但继续执行
2025-07-30 14:59:19,197 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 14:59:19,198 - modules.friend_request_window - INFO - ✅ 切换窗口后主界面操作流程执行成功
2025-07-30 14:59:19,199 - modules.friend_request_window - INFO - 🔧 开始执行添加朋友窗口位置同步...
2025-07-30 14:59:19,199 - modules.friend_request_window - INFO - 🎯 开始同步添加朋友窗口位置到标准坐标 (1200, 0)...
2025-07-30 14:59:20,202 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 14:59:20,203 - modules.friend_request_window - WARNING - ⚠️ 未找到添加朋友窗口，尝试等待窗口出现...
2025-07-30 14:59:22,205 - modules.friend_request_window - INFO - 🔍 共找到 0 个添加朋友窗口
2025-07-30 14:59:22,206 - modules.friend_request_window - ERROR - ❌ 仍未找到添加朋友窗口，位置同步失败
2025-07-30 14:59:22,206 - modules.friend_request_window - WARNING - ⚠️ 添加朋友窗口位置同步失败，但继续执行
2025-07-30 14:59:22,206 - modules.friend_request_window - INFO - ✅ 微信主界面点击操作已完成，可以继续后续操作
2025-07-30 14:59:22,206 - modules.friend_request_window - INFO - 🔄 已设置重新开始标志
2025-07-30 14:59:22,207 - modules.friend_request_window - INFO - 🔄 频率错误处理完成，已设置重新开始标志
2025-07-30 14:59:22,207 - modules.friend_request_window - INFO - ✅ 频率错误处理完成
2025-07-30 14:59:22,207 - modules.friend_request_window - INFO - ✅ 原有频率错误处理成功，已切换到微信2
2025-07-30 14:59:22,207 - modules.friend_request_window - WARNING - ⚠️ 检测到并处理了频率错误，已切换到微信2
2025-07-30 14:59:22,208 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 14:59:22,208 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 14:59:22,208 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 14:59:22,208 - modules.friend_request_window - INFO -    📝 备注信息: '014325120001-宋刘爱迪-2025-07-30 22:58:20'
2025-07-30 14:59:22,709 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 14:59:22,710 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:59:22,711 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:59:22,713 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 14:59:22,713 - modules.wechat_auto_add_simple - INFO - ✅ 18513345287 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 14:59:22,714 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 18513345287
2025-07-30 14:59:22,715 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 14:59:22,715 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 14:59:22,717 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 14:59:24,039 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 14:59:24,039 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 14:59:24,039 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 14:59:24,041 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 14:59:24,041 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 14:59:24,041 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 14:59:24,041 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 14:59:24,042 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 14:59:24,042 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 14:59:24,043 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 14:59:24,043 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 14:59:24,044 - __main__ - INFO - � 更新全局进度：已处理 2/2840 个联系人（剩余 2840 个）
2025-07-30 14:59:24,044 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 14:59:27,045 - __main__ - INFO - 
============================================================
2025-07-30 14:59:27,047 - __main__ - INFO - 🎯 开始处理第 2/3 个微信窗口 (第 1 轮)
2025-07-30 14:59:27,050 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 14:59:27,051 - __main__ - INFO - 📊 全局进度：已处理 2/2840 个联系人（剩余 2840 个）
2025-07-30 14:59:27,052 - __main__ - INFO - ============================================================
2025-07-30 14:59:27,054 - __main__ - INFO - 🚀 开始处理微信窗口 2
2025-07-30 14:59:27,054 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 6032430)
2025-07-30 14:59:27,054 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 14:59:27,055 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 2
2025-07-30 14:59:27,055 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 6032430)
2025-07-30 14:59:27,056 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-30 14:59:27,056 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 14:59:28,057 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 14:59:29,205 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 14:59:29,205 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 6032430
2025-07-30 14:59:29,206 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 6032430) - 增强版
2025-07-30 14:59:29,509 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 14:59:29,509 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 14:59:29,510 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 14:59:29,510 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 14:59:29,511 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 14:59:29,511 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 14:59:29,511 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 14:59:29,512 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 14:59:29,512 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 14:59:29,512 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 14:59:29,714 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 14:59:29,714 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 14:59:29,717 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 6032430 (API返回: None)
2025-07-30 14:59:30,018 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 14:59:30,019 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 14:59:30,019 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 14:59:31,019 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 14:59:31,020 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 2
2025-07-30 14:59:31,020 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 14:59:31,020 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 14:59:31,020 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 14:59:31,021 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 14:59:31,021 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 14:59:31,021 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 14:59:31,021 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 14:59:31,222 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 14:59:31,223 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 14:59:31,223 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 14:59:33,634 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 14:59:33,634 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 14:59:33,634 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
2025-07-30 14:59:36,093 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 14:59:36,296 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 14:59:36,297 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 14:59:36,298 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 14:59:38,684 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 14:59:38,702 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 14:59:38,703 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 14:59:40,320 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 14:59:40,521 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 14:59:40,521 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 14:59:40,522 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 14:59:42,899 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 14:59:42,900 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 14:59:42,900 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.1 秒...
2025-07-30 14:59:45,043 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 14:59:45,244 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 14:59:45,245 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 14:59:45,246 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 14:59:47,622 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 14:59:47,623 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 14:59:47,623 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 14:59:49,402 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 14:59:49,603 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 14:59:49,603 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 14:59:49,604 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
